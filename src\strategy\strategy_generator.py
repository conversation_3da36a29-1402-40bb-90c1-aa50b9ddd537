"""
Strategy Generator

Automatically generates trading strategies from Pine Script indicators
by analyzing signal patterns and creating optimized entry/exit rules.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import re
from .base_strategy import BaseStrategy, SignalType, PositionType


class StrategyGenerator:
    """
    Generates trading strategies from Pine Script indicators
    
    Analyzes the indicator logic to:
    - Identify entry and exit conditions
    - Determine signal strength thresholds
    - Generate risk management rules
    - Create optimized strategy parameters
    """
    
    def __init__(self):
        self.signal_patterns = self._build_signal_patterns()
        self.risk_patterns = self._build_risk_patterns()
    
    def _build_signal_patterns(self) -> Dict[str, Dict]:
        """Build patterns for detecting trading signals in Pine Script"""
        return {
            # Trend-based signals
            'trend_score_bullish': {
                'pattern': r'trend_score\s*>\s*(\d+)',
                'signal_type': SignalType.BUY,
                'description': 'Bullish when trend score above threshold'
            },
            'trend_score_bearish': {
                'pattern': r'trend_score\s*<\s*(\d+)',
                'signal_type': SignalType.SELL,
                'description': 'Bearish when trend score below threshold'
            },
            'strong_trend_bullish': {
                'pattern': r'trend_score\s*>\s*(\d+).*strong',
                'signal_type': SignalType.STRONG_BUY,
                'description': 'Strong bullish signal'
            },
            'strong_trend_bearish': {
                'pattern': r'trend_score\s*<\s*(\d+).*strong',
                'signal_type': SignalType.STRONG_SELL,
                'description': 'Strong bearish signal'
            },
            
            # MACD signals
            'macd_bullish_cross': {
                'pattern': r'macd.*cross.*above|macd.*>\s*signal',
                'signal_type': SignalType.BUY,
                'description': 'MACD bullish crossover'
            },
            'macd_bearish_cross': {
                'pattern': r'macd.*cross.*below|macd.*<\s*signal',
                'signal_type': SignalType.SELL,
                'description': 'MACD bearish crossover'
            },
            
            # RSI signals
            'rsi_oversold': {
                'pattern': r'rsi\s*<\s*(\d+)',
                'signal_type': SignalType.BUY,
                'description': 'RSI oversold condition'
            },
            'rsi_overbought': {
                'pattern': r'rsi\s*>\s*(\d+)',
                'signal_type': SignalType.SELL,
                'description': 'RSI overbought condition'
            },
            
            # Volume signals
            'volume_breakout': {
                'pattern': r'volume.*>\s*.*average|volume.*spike',
                'signal_type': SignalType.BUY,
                'description': 'Volume breakout signal'
            }
        }
    
    def _build_risk_patterns(self) -> Dict[str, Dict]:
        """Build patterns for risk management rules"""
        return {
            'stop_loss': {
                'pattern': r'stop.*loss|sl\s*=\s*([\d.]+)',
                'description': 'Stop loss percentage'
            },
            'take_profit': {
                'pattern': r'take.*profit|tp\s*=\s*([\d.]+)',
                'description': 'Take profit percentage'
            },
            'position_size': {
                'pattern': r'position.*size|size\s*=\s*([\d.]+)',
                'description': 'Position size percentage'
            }
        }
    
    def generate_strategy_from_pine_script(self, pine_script_code: str, 
                                         indicator_class: Any) -> 'GeneratedStrategy':
        """
        Generate a trading strategy from Pine Script code and indicator
        
        Args:
            pine_script_code: Original Pine Script source code
            indicator_class: Converted Python indicator class
            
        Returns:
            Generated strategy class
        """
        # Analyze Pine Script for signal patterns
        signal_rules = self._extract_signal_rules(pine_script_code)
        risk_rules = self._extract_risk_rules(pine_script_code)
        
        # Generate strategy parameters
        strategy_params = self._generate_strategy_parameters(signal_rules, risk_rules)
        
        # Create strategy class
        strategy = GeneratedStrategy(
            indicator=indicator_class,
            signal_rules=signal_rules,
            risk_rules=risk_rules,
            **strategy_params
        )
        
        return strategy
    
    def _extract_signal_rules(self, pine_script_code: str) -> List[Dict]:
        """Extract trading signal rules from Pine Script code"""
        signal_rules = []
        lines = pine_script_code.split('\n')
        
        for line_num, line in enumerate(lines):
            line = line.strip().lower()
            
            # Skip comments and empty lines
            if not line or line.startswith('//'):
                continue
            
            # Check each signal pattern
            for pattern_name, pattern_info in self.signal_patterns.items():
                match = re.search(pattern_info['pattern'], line, re.IGNORECASE)
                if match:
                    # Extract threshold if present
                    threshold = None
                    if match.groups():
                        try:
                            threshold = float(match.group(1))
                        except (ValueError, IndexError):
                            threshold = None
                    
                    signal_rule = {
                        'name': pattern_name,
                        'signal_type': pattern_info['signal_type'],
                        'description': pattern_info['description'],
                        'threshold': threshold,
                        'line_number': line_num + 1,
                        'original_line': line
                    }
                    signal_rules.append(signal_rule)
        
        return signal_rules
    
    def _extract_risk_rules(self, pine_script_code: str) -> Dict[str, Any]:
        """Extract risk management rules from Pine Script code"""
        risk_rules = {}
        lines = pine_script_code.split('\n')
        
        for line in lines:
            line = line.strip().lower()
            
            # Skip comments and empty lines
            if not line or line.startswith('//'):
                continue
            
            # Check each risk pattern
            for pattern_name, pattern_info in self.risk_patterns.items():
                match = re.search(pattern_info['pattern'], line, re.IGNORECASE)
                if match and match.groups():
                    try:
                        value = float(match.group(1))
                        risk_rules[pattern_name] = {
                            'value': value,
                            'description': pattern_info['description'],
                            'original_line': line
                        }
                    except (ValueError, IndexError):
                        continue
        
        return risk_rules
    
    def _generate_strategy_parameters(self, signal_rules: List[Dict], 
                                    risk_rules: Dict[str, Any]) -> Dict[str, Any]:
        """Generate strategy parameters based on extracted rules"""
        params = {
            'initial_capital': 10000.0,
            'position_size': 0.1,
            'stop_loss': 0.02,
            'take_profit': 0.04,
            'max_positions': 1,
            'commission': 0.001
        }
        
        # Update parameters based on risk rules
        if 'stop_loss' in risk_rules:
            params['stop_loss'] = risk_rules['stop_loss']['value'] / 100
        
        if 'take_profit' in risk_rules:
            params['take_profit'] = risk_rules['take_profit']['value'] / 100
        
        if 'position_size' in risk_rules:
            params['position_size'] = risk_rules['position_size']['value'] / 100
        
        # Adjust parameters based on signal complexity
        signal_count = len(signal_rules)
        if signal_count > 5:  # Complex strategy
            params['max_positions'] = 2
            params['position_size'] *= 0.8  # Reduce position size for complex strategies
        
        return params
    
    def generate_brandon_algo_strategy(self, brandon_indicator) -> 'BrandonAlgoStrategy':
        """Generate strategy specifically for Brandon Algo indicator"""
        return BrandonAlgoStrategy(brandon_indicator)


class GeneratedStrategy(BaseStrategy):
    """
    Generated trading strategy based on Pine Script analysis
    """
    
    def __init__(self, indicator, signal_rules: List[Dict], risk_rules: Dict[str, Any], **kwargs):
        super().__init__(**kwargs)
        self.indicator = indicator
        self.signal_rules = signal_rules
        self.risk_rules = risk_rules
        
        # Extract signal thresholds
        self.bullish_threshold = self._get_threshold('trend_score_bullish', 70)
        self.bearish_threshold = self._get_threshold('trend_score_bearish', 30)
        self.strong_bullish_threshold = self._get_threshold('strong_trend_bullish', 80)
        self.strong_bearish_threshold = self._get_threshold('strong_trend_bearish', 20)
    
    def _get_threshold(self, rule_name: str, default: float) -> float:
        """Get threshold value for a specific rule"""
        for rule in self.signal_rules:
            if rule['name'] == rule_name and rule['threshold'] is not None:
                return rule['threshold']
        return default
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate trading signals from indicator data"""
        # Calculate indicator values
        indicator_data = self.indicator.calculate(data)
        
        # Generate signals based on rules
        signals = pd.DataFrame(index=data.index)
        signals['signal'] = SignalType.HOLD.value
        signals['signal_strength'] = 0.0
        signals['trend_score'] = indicator_data['trend_score']
        
        # Apply signal rules
        for _, row in indicator_data.iterrows():
            signal, strength = self._evaluate_signals(row)
            signals.loc[row.name, 'signal'] = signal.value
            signals.loc[row.name, 'signal_strength'] = strength
        
        return signals
    
    def _evaluate_signals(self, indicator_row) -> Tuple[SignalType, float]:
        """Evaluate signals for a single row of indicator data"""
        trend_score = indicator_row['trend_score']
        
        # Strong signals
        if trend_score > self.strong_bullish_threshold:
            return SignalType.STRONG_BUY, min(1.0, (trend_score - self.strong_bullish_threshold) / 20)
        elif trend_score < self.strong_bearish_threshold:
            return SignalType.STRONG_SELL, min(1.0, (self.strong_bearish_threshold - trend_score) / 20)
        
        # Regular signals
        elif trend_score > self.bullish_threshold:
            return SignalType.BUY, min(1.0, (trend_score - self.bullish_threshold) / 30)
        elif trend_score < self.bearish_threshold:
            return SignalType.SELL, min(1.0, (self.bearish_threshold - trend_score) / 30)
        
        # Hold
        return SignalType.HOLD, 0.0


class BrandonAlgoStrategy(BaseStrategy):
    """
    Specialized strategy for Brandon Algo indicator
    """
    
    def __init__(self, brandon_indicator, **kwargs):
        # Default parameters optimized for Brandon Algo
        default_params = {
            'initial_capital': 10000.0,
            'position_size': 0.15,  # Slightly larger position size
            'stop_loss': 0.025,     # 2.5% stop loss
            'take_profit': 0.05,    # 5% take profit
            'max_positions': 2,     # Allow 2 concurrent positions
            'commission': 0.001
        }
        default_params.update(kwargs)
        
        super().__init__(**default_params)
        self.indicator = brandon_indicator
        
        # Brandon Algo specific thresholds
        self.strong_bullish_threshold = 80
        self.bullish_threshold = 70
        self.neutral_upper = 60
        self.neutral_lower = 40
        self.bearish_threshold = 30
        self.strong_bearish_threshold = 20
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate signals using Brandon Algo logic"""
        # Get indicator signals
        signals = self.indicator.get_signals(data)
        
        # Enhance with additional logic
        signals['enhanced_signal'] = signals['signal']
        signals['position_sizing'] = 1.0
        
        # Adjust position sizing based on signal strength
        strong_mask = signals['signal'].isin(['STRONG_BUY', 'STRONG_SELL'])
        signals.loc[strong_mask, 'position_sizing'] = 1.5
        
        # Add trend confirmation
        signals['trend_confirmed'] = (
            (signals['trend_score'] > self.bullish_threshold) & 
            (signals['trend_score'].shift(1) > signals['trend_score'].shift(2))
        ) | (
            (signals['trend_score'] < self.bearish_threshold) & 
            (signals['trend_score'].shift(1) < signals['trend_score'].shift(2))
        )
        
        return signals
    
    def backtest(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Run backtest on historical data"""
        signals = self.generate_signals(data)
        
        # Process each signal
        for timestamp, row in signals.iterrows():
            price = data.loc[timestamp, 'close']
            signal_type = SignalType(row['signal'])
            signal_strength = row.get('signal_strength', 1.0)
            
            self.process_signal(timestamp, price, signal_type, signal_strength)
        
        # Close any remaining positions at the end
        if self.positions:
            final_price = data['close'].iloc[-1]
            final_timestamp = data.index[-1]
            
            for position in self.positions.copy():
                self._close_position(final_timestamp, final_price, position, SignalType.HOLD)
        
        return self.get_performance_summary()

"""
Pine Script Lexer

Tokenizes Pine Script source code into meaningful tokens
for parsing and analysis.
"""

import re
from typing import List, NamedTuple, Iterator
from enum import Enum


class TokenType(Enum):
    # Literals
    NUMBER = "NUMBER"
    STRING = "STRING"
    BOOLEAN = "BOOLEAN"
    COLOR = "COLOR"
    
    # Identifiers and keywords
    IDENTIFIER = "IDENTIFIER"
    KEYWORD = "KEYWORD"
    
    # Operators
    ASSIGN = "ASSIGN"
    PLUS = "PLUS"
    MINUS = "MINUS"
    MULTIPLY = "MULTIPLY"
    DIVIDE = "DIVIDE"
    MODULO = "MODULO"
    POWER = "POWER"
    
    # Comparison operators
    EQUAL = "EQUAL"
    NOT_EQUAL = "NOT_EQUAL"
    GREATER = "GREATER"
    LESS = "LESS"
    GREATER_EQUAL = "GREATER_EQUAL"
    LESS_EQUAL = "LESS_EQUAL"
    
    # Logical operators
    AND = "AND"
    OR = "OR"
    NOT = "NOT"
    
    # Punctuation
    LPAREN = "LPAREN"
    RPAREN = "RPAREN"
    LBRACKET = "LBRACKET"
    RBRACKET = "RBRACKET"
    COMMA = "COMMA"
    DOT = "DOT"
    QUESTION = "QUESTION"
    COLON = "COLON"
    
    # Special
    NEWLINE = "NEWLINE"
    INDENT = "INDENT"
    DEDENT = "DEDENT"
    EOF = "EOF"
    COMMENT = "COMMENT"
    
    # Pine Script specific
    VERSION = "VERSION"
    INDICATOR = "INDICATOR"
    INPUT = "INPUT"
    VAR = "VAR"
    CONST = "CONST"
    PLOT = "PLOT"
    ALERT = "ALERT"


class Token(NamedTuple):
    type: TokenType
    value: str
    line: int
    column: int


class PineScriptLexer:
    """Lexical analyzer for Pine Script"""
    
    KEYWORDS = {
        'indicator', 'strategy', 'library',
        'input', 'var', 'const', 'if', 'else', 'for', 'while',
        'true', 'false', 'na', 'and', 'or', 'not',
        'plot', 'plotshape', 'plotchar', 'plotcandle', 'plotbar',
        'alert', 'alertcondition', 'bgcolor', 'fill',
        'import', 'export', 'method', 'type'
    }
    
    # Token patterns (order matters!)
    TOKEN_PATTERNS = [
        (r'//.*', TokenType.COMMENT),
        (r'//@version=\d+', TokenType.VERSION),
        (r'\d+\.\d+', TokenType.NUMBER),
        (r'\d+', TokenType.NUMBER),
        (r'"[^"]*"', TokenType.STRING),
        (r"'[^']*'", TokenType.STRING),
        (r'#[0-9a-fA-F]{6,8}', TokenType.COLOR),
        (r'[a-zA-Z_][a-zA-Z0-9_]*', TokenType.IDENTIFIER),
        (r':=', TokenType.ASSIGN),
        (r'==', TokenType.EQUAL),
        (r'!=', TokenType.NOT_EQUAL),
        (r'>=', TokenType.GREATER_EQUAL),
        (r'<=', TokenType.LESS_EQUAL),
        (r'=>', TokenType.ASSIGN),
        (r'\+', TokenType.PLUS),
        (r'-', TokenType.MINUS),
        (r'\*', TokenType.MULTIPLY),
        (r'/', TokenType.DIVIDE),
        (r'%', TokenType.MODULO),
        (r'\^', TokenType.POWER),
        (r'>', TokenType.GREATER),
        (r'<', TokenType.LESS),
        (r'=', TokenType.ASSIGN),
        (r'\(', TokenType.LPAREN),
        (r'\)', TokenType.RPAREN),
        (r'\[', TokenType.LBRACKET),
        (r'\]', TokenType.RBRACKET),
        (r',', TokenType.COMMA),
        (r'\.', TokenType.DOT),
        (r'\?', TokenType.QUESTION),
        (r':', TokenType.COLON),
        (r'\n', TokenType.NEWLINE),
        (r'[ \t]+', None),  # Skip whitespace
    ]
    
    def __init__(self, source_code: str):
        self.source_code = source_code
        self.lines = source_code.split('\n')
        self.tokens: List[Token] = []
        self.current_line = 1
        self.current_column = 1
        
    def tokenize(self) -> List[Token]:
        """Tokenize the entire source code"""
        self.tokens = []
        
        for line_num, line in enumerate(self.lines, 1):
            self.current_line = line_num
            self.current_column = 1
            self._tokenize_line(line)
            
        self.tokens.append(Token(TokenType.EOF, '', self.current_line, self.current_column))
        return self.tokens
    
    def _tokenize_line(self, line: str) -> None:
        """Tokenize a single line"""
        pos = 0
        
        while pos < len(line):
            matched = False
            
            for pattern, token_type in self.TOKEN_PATTERNS:
                regex = re.compile(pattern)
                match = regex.match(line, pos)
                
                if match:
                    value = match.group(0)
                    
                    if token_type is not None:  # Skip whitespace
                        # Handle keywords
                        if token_type == TokenType.IDENTIFIER and value in self.KEYWORDS:
                            token_type = TokenType.KEYWORD
                        
                        # Handle boolean literals
                        if value in ('true', 'false'):
                            token_type = TokenType.BOOLEAN
                            
                        self.tokens.append(Token(
                            token_type, 
                            value, 
                            self.current_line, 
                            self.current_column + pos
                        ))
                    
                    pos = match.end()
                    matched = True
                    break
            
            if not matched:
                # Skip unknown characters
                pos += 1
        
        # Add newline token at end of line
        if line.strip():  # Only if line is not empty
            self.tokens.append(Token(
                TokenType.NEWLINE, 
                '\n', 
                self.current_line, 
                len(line) + 1
            ))
    
    def get_tokens_by_type(self, token_type: TokenType) -> List[Token]:
        """Get all tokens of a specific type"""
        return [token for token in self.tokens if token.type == token_type]
    
    def get_line_tokens(self, line_number: int) -> List[Token]:
        """Get all tokens from a specific line"""
        return [token for token in self.tokens if token.line == line_number]

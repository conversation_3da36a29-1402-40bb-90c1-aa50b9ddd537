"""
Abstract Syntax Tree (AST) nodes for Pine Script parsing

These classes represent different components of Pine Script code
in a structured format that can be easily converted to Python.
"""

from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Union
from enum import Enum


class PineScriptVersion(Enum):
    V4 = "4"
    V5 = "5" 
    V6 = "6"


class InputType(Enum):
    BOOL = "bool"
    INT = "int"
    FLOAT = "float"
    STRING = "string"
    COLOR = "color"


class PlotStyle(Enum):
    LINE = "line"
    HISTOGRAM = "histogram"
    CROSS = "cross"
    CIRCLES = "circles"
    COLUMNS = "columns"


@dataclass
class InputParameter:
    """Represents an input parameter in Pine Script"""
    name: str
    input_type: InputType
    default_value: Any
    title: str = ""
    tooltip: str = ""
    group: str = ""
    minval: Optional[float] = None
    maxval: Optional[float] = None
    step: Optional[float] = None
    options: Optional[List[str]] = None


@dataclass
class Variable:
    """Represents a variable declaration or assignment"""
    name: str
    value: Any
    var_type: Optional[str] = None
    is_series: bool = False
    is_const: bool = False


@dataclass
class FunctionCall:
    """Represents a function call with arguments"""
    name: str
    args: List[Any]
    kwargs: Dict[str, Any]


@dataclass
class FunctionDefinition:
    """Represents a user-defined function"""
    name: str
    parameters: List[str]
    body: List[Any]
    return_type: Optional[str] = None


@dataclass
class PlotStatement:
    """Represents a plot() statement"""
    series: str
    title: str = ""
    color: str = ""
    linewidth: int = 1
    style: PlotStyle = PlotStyle.LINE
    display: str = "all"


@dataclass
class AlertCondition:
    """Represents an alert condition"""
    condition: str
    title: str = ""
    message: str = ""
    freq: str = "once_per_bar"


@dataclass
class IndicatorDeclaration:
    """Represents the main indicator() declaration"""
    title: str
    shorttitle: str = ""
    overlay: bool = False
    format: str = "price"
    precision: int = 4
    scale: str = "right"


@dataclass
class PineScriptAST:
    """Root AST node containing all parsed components"""
    version: PineScriptVersion
    indicator: IndicatorDeclaration
    inputs: List[InputParameter]
    constants: List[Variable]
    variables: List[Variable]
    functions: List[FunctionDefinition]
    calculations: List[Any]
    plots: List[PlotStatement]
    alerts: List[AlertCondition]
    raw_code: str = ""

"""
Strategy Generation Module

Automatically detects trading signals from Pine Script indicators
and generates Python strategy classes with entry/exit conditions
and risk management.
"""

from .strategy_generator import StrategyGenerator
from .signal_detector import SignalDetector
from .risk_manager import RiskManager
from .base_strategy import BaseStrategy

__all__ = [
    "StrategyGenerator",
    "SignalDetector",
    "RiskManager", 
    "BaseStrategy"
]

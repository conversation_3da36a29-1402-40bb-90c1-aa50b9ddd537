"""
Template Engine for Python Code Generation

Provides templates and utilities for generating clean, 
well-structured Python code from Pine Script AST.
"""

from typing import Dict, List, Any, Optional
from ..parser.ast_nodes import PineScriptAST, InputParameter, InputType


class TemplateEngine:
    """Generates Python code using templates"""
    
    def __init__(self):
        self.templates = self._load_templates()
    
    def _load_templates(self) -> Dict[str, str]:
        """Load code generation templates"""
        return {
            'class_header': self._get_class_header_template(),
            'init_method': self._get_init_method_template(),
            'calculate_method': self._get_calculate_method_template(),
            'custom_functions': self._get_custom_functions_template(),
            'imports': self._get_imports_template()
        }
    
    def _get_class_header_template(self) -> str:
        """Template for class header and docstring"""
        return '''"""
{title}

Converted from Pine Script indicator.
Original Pine Script version: {version}

This class implements the indicator logic using pandas and numpy.
"""

import pandas as pd
import numpy as np
{additional_imports}


class {class_name}:
    """
    {title}
    
    Parameters:
{parameters_doc}
    """
    
    def __init__(self, {init_params}):
        """Initialize the indicator with parameters"""
{init_body}
        
        # Internal state variables
        self._current_index = 0
        self._data_length = 0
        self._results = {{}}
'''
    
    def _get_init_method_template(self) -> str:
        """Template for __init__ method"""
        return '''        # Set parameters
{parameter_assignments}
        
        # Initialize arrays and variables
{variable_initializations}
'''
    
    def _get_calculate_method_template(self) -> str:
        """Template for main calculate method"""
        return '''    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate indicator values for the given OHLCV data
        
        Args:
            data: DataFrame with columns ['open', 'high', 'low', 'close', 'volume']
            
        Returns:
            DataFrame with calculated indicator values
        """
        if data.empty:
            return pd.DataFrame()
        
        # Validate required columns
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {{missing_columns}}")
        
        # Extract OHLCV data
        open_prices = data['open'].values
        high_prices = data['high'].values
        low_prices = data['low'].values
        close_prices = data['close'].values
        volume = data['volume'].values
        
        self._data_length = len(data)
        
        # Initialize result arrays
{result_initializations}
        
        # Main calculation loop
        for i in range(self._data_length):
            self._current_index = i
            
{calculation_body}
        
        # Create result DataFrame
        result_df = data.copy()
{result_assignments}
        
        return result_df
'''
    
    def _get_custom_functions_template(self) -> str:
        """Template for custom function implementations"""
        return '''
    # Custom function implementations
    
    def _rma(self, series: np.ndarray, length: int) -> np.ndarray:
        """Running Moving Average (Pine Script ta.rma equivalent)"""
        result = np.full_like(series, np.nan)
        if len(series) == 0 or length <= 0:
            return result
            
        alpha = 1.0 / length
        result[0] = series[0]
        
        for i in range(1, len(series)):
            if not np.isnan(series[i]):
                if np.isnan(result[i-1]):
                    result[i] = series[i]
                else:
                    result[i] = alpha * series[i] + (1 - alpha) * result[i-1]
            else:
                result[i] = result[i-1]
                
        return result
    
    def _vwma(self, close: np.ndarray, volume: np.ndarray, length: int) -> np.ndarray:
        """Volume Weighted Moving Average"""
        result = np.full_like(close, np.nan)
        
        for i in range(length - 1, len(close)):
            start_idx = max(0, i - length + 1)
            close_slice = close[start_idx:i+1]
            volume_slice = volume[start_idx:i+1]
            
            if len(close_slice) > 0 and np.sum(volume_slice) > 0:
                result[i] = np.sum(close_slice * volume_slice) / np.sum(volume_slice)
                
        return result
    
    def _change(self, series: np.ndarray, length: int = 1) -> np.ndarray:
        """Calculate change over n periods"""
        result = np.full_like(series, np.nan)
        
        for i in range(length, len(series)):
            result[i] = series[i] - series[i - length]
            
        return result
    
    def _stdev(self, series: np.ndarray, length: int) -> np.ndarray:
        """Rolling standard deviation"""
        result = np.full_like(series, np.nan)
        
        for i in range(length - 1, len(series)):
            start_idx = max(0, i - length + 1)
            slice_data = series[start_idx:i+1]
            result[i] = np.std(slice_data, ddof=0)
            
        return result
    
    def _rising(self, series: np.ndarray, length: int) -> np.ndarray:
        """Check if series is rising over length periods"""
        result = np.full(len(series), False, dtype=bool)
        
        for i in range(length, len(series)):
            if not np.isnan(series[i]) and not np.isnan(series[i - length]):
                result[i] = series[i] > series[i - length]
                
        return result
    
    def _falling(self, series: np.ndarray, length: int) -> np.ndarray:
        """Check if series is falling over length periods"""
        result = np.full(len(series), False, dtype=bool)
        
        for i in range(length, len(series)):
            if not np.isnan(series[i]) and not np.isnan(series[i - length]):
                result[i] = series[i] < series[i - length]
                
        return result
    
    def _is_first_bar(self) -> bool:
        """Check if current bar is the first bar"""
        return self._current_index == 0
    
    def _is_last_bar(self) -> bool:
        """Check if current bar is the last bar"""
        return self._current_index == self._data_length - 1
    
    def _dmi(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, 
             length: int, adx_smoothing: int) -> tuple:
        """Directional Movement Index calculation"""
        # Simplified DMI implementation
        # In a full implementation, this would be more comprehensive
        tr = np.maximum(high - low, 
                       np.maximum(np.abs(high - np.roll(close, 1)),
                                 np.abs(low - np.roll(close, 1))))
        
        dm_plus = np.where((high - np.roll(high, 1)) > (np.roll(low, 1) - low),
                          np.maximum(high - np.roll(high, 1), 0), 0)
        dm_minus = np.where((np.roll(low, 1) - low) > (high - np.roll(high, 1)),
                           np.maximum(np.roll(low, 1) - low, 0), 0)
        
        # Use simple moving averages for smoothing (simplified)
        tr_smooth = pd.Series(tr).rolling(length).mean().values
        dm_plus_smooth = pd.Series(dm_plus).rolling(length).mean().values
        dm_minus_smooth = pd.Series(dm_minus).rolling(length).mean().values
        
        di_plus = 100 * dm_plus_smooth / tr_smooth
        di_minus = 100 * dm_minus_smooth / tr_smooth
        
        dx = 100 * np.abs(di_plus - di_minus) / (di_plus + di_minus)
        adx = pd.Series(dx).rolling(adx_smoothing).mean().values
        
        return di_plus, di_minus, adx
'''
    
    def _get_imports_template(self) -> str:
        """Template for import statements"""
        return '''import pandas as pd
import numpy as np
{additional_imports}
'''
    
    def generate_class_name(self, title: str) -> str:
        """Generate a valid Python class name from indicator title"""
        # Remove special characters and convert to PascalCase
        import re
        
        # Remove brackets and special characters
        clean_title = re.sub(r'[^\w\s]', '', title)
        
        # Split into words and capitalize each
        words = clean_title.split()
        class_name = ''.join(word.capitalize() for word in words if word)
        
        # Ensure it starts with a letter
        if not class_name or not class_name[0].isalpha():
            class_name = 'Indicator' + class_name
        
        return class_name
    
    def generate_parameter_docs(self, inputs: List[InputParameter]) -> str:
        """Generate parameter documentation"""
        docs = []
        
        for param in inputs:
            type_str = param.input_type.value
            default_str = f" (default: {param.default_value})" if param.default_value is not None else ""
            docs.append(f"        {param.name} ({type_str}): {param.title}{default_str}")
        
        return '\n'.join(docs) if docs else "        No parameters"
    
    def generate_init_params(self, inputs: List[InputParameter]) -> str:
        """Generate __init__ method parameters"""
        params = []
        
        for param in inputs:
            if param.default_value is not None:
                if isinstance(param.default_value, str):
                    default = f'"{param.default_value}"'
                else:
                    default = str(param.default_value)
                params.append(f"{param.name}={default}")
            else:
                params.append(param.name)
        
        return ', '.join(params) if params else ""
    
    def generate_parameter_assignments(self, inputs: List[InputParameter]) -> str:
        """Generate parameter assignment statements"""
        assignments = []
        
        for param in inputs:
            assignments.append(f"        self.{param.name} = {param.name}")
        
        return '\n'.join(assignments) if assignments else "        pass"

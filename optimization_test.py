#!/usr/bin/env python3
"""
Parameter Optimization Test

Tests the parameter optimization system with the Brandon Algo indicator.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add src to path
sys.path.append('src')
sys.path.append('.')

from brandon_algo_simple import SimpleBrandonAlgo
from src.optimization.parameter_optimizer import ParameterOptimizer, ParameterRange


def generate_test_data(days: int = 500) -> pd.DataFrame:
    """Generate test data for optimization"""
    print(f"📊 Generating {days} days of test data...")
    
    dates = pd.date_range(start=datetime.now() - timedelta(days=days), 
                         end=datetime.now(), freq='D')
    
    # Generate more realistic price movement with trends
    np.random.seed(42)
    
    base_price = 50000.0
    returns = np.random.normal(0.0005, 0.02, len(dates))  # Small positive drift
    
    # Add trend periods
    trend_changes = np.random.choice(len(dates), size=5, replace=False)
    trend_changes = np.sort(trend_changes)
    
    for i in range(len(trend_changes) - 1):
        start_idx = trend_changes[i]
        end_idx = trend_changes[i + 1]
        trend_strength = np.random.uniform(-0.001, 0.002)  # Random trend
        trend = np.linspace(0, trend_strength * (end_idx - start_idx), end_idx - start_idx)
        returns[start_idx:end_idx] += trend
    
    # Add volatility clustering
    volatility = np.abs(returns)
    for i in range(1, len(volatility)):
        volatility[i] = 0.7 * volatility[i-1] + 0.3 * volatility[i]
    
    returns = returns * (1 + volatility)
    
    cumulative_returns = np.cumsum(returns)
    close_prices = base_price * np.exp(cumulative_returns)
    
    # Generate OHLV
    data = []
    for i, (date, close) in enumerate(zip(dates, close_prices)):
        vol = abs(returns[i]) * close
        
        high = close + np.random.uniform(0, vol * 2)
        low = close - np.random.uniform(0, vol * 2)
        
        if i == 0:
            open_price = close
        else:
            open_price = close_prices[i-1] + np.random.normal(0, vol * 0.1)
        
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        base_volume = 1000000
        volume_multiplier = 1 + abs(returns[i]) * 20
        volume = base_volume * volume_multiplier * np.random.uniform(0.5, 2.0)
        
        data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        })
    
    df = pd.DataFrame(data, index=dates)
    print(f"✅ Generated test data: {len(df)} days")
    return df


def test_grid_search_optimization():
    """Test grid search optimization"""
    print(f"\n🔍 Testing Grid Search Optimization...")
    
    # Generate test data
    data = generate_test_data(400)
    
    # Define parameter ranges for optimization
    parameter_ranges = [
        ParameterRange(
            name='rsi_period',
            min_value=10,
            max_value=20,
            step=2,
            param_type='int'
        ),
        ParameterRange(
            name='macd_fast',
            min_value=8,
            max_value=16,
            step=2,
            param_type='int'
        ),
        ParameterRange(
            name='macd_slow',
            min_value=20,
            max_value=30,
            step=2,
            param_type='int'
        ),
        ParameterRange(
            name='bullish_threshold',
            min_value=65,
            max_value=75,
            step=2.5,
            param_type='float'
        ),
        ParameterRange(
            name='bearish_threshold',
            min_value=25,
            max_value=35,
            step=2.5,
            param_type='float'
        )
    ]
    
    # Initialize optimizer
    optimizer = ParameterOptimizer(
        strategy_class=SimpleBrandonAlgo,
        data=data,
        optimization_metric='sharpe_ratio',
        train_ratio=0.6,
        validation_ratio=0.2,
        test_ratio=0.2
    )
    
    # Run grid search
    results = optimizer.grid_search(parameter_ranges, max_combinations=100)
    
    print(f"\n📊 Grid Search Results:")
    print(f"   Best Sharpe Ratio: {results.best_score:.4f}")
    print(f"   Best Parameters: {results.best_params}")
    print(f"   Optimization Time: {results.optimization_time:.1f} seconds")
    print(f"   Total Combinations Tested: {len(results.all_results)}")
    
    # Validate on validation set
    validation_results = optimizer.validate_best_parameters()
    
    # Test on test set
    test_results = optimizer.test_final_performance()
    
    return optimizer, results


def test_random_search_optimization():
    """Test random search optimization"""
    print(f"\n🎲 Testing Random Search Optimization...")
    
    # Generate test data
    data = generate_test_data(400)
    
    # Define parameter ranges
    parameter_ranges = [
        ParameterRange(
            name='rsi_period',
            min_value=5,
            max_value=25,
            step=1,
            param_type='int'
        ),
        ParameterRange(
            name='macd_fast',
            min_value=5,
            max_value=20,
            step=1,
            param_type='int'
        ),
        ParameterRange(
            name='macd_slow',
            min_value=15,
            max_value=35,
            step=1,
            param_type='int'
        ),
        ParameterRange(
            name='bullish_threshold',
            min_value=60,
            max_value=80,
            step=0.1,
            param_type='float'
        ),
        ParameterRange(
            name='bearish_threshold',
            min_value=20,
            max_value=40,
            step=0.1,
            param_type='float'
        ),
        ParameterRange(
            name='trend_period',
            min_value=10,
            max_value=30,
            step=1,
            param_type='int'
        )
    ]
    
    # Initialize optimizer
    optimizer = ParameterOptimizer(
        strategy_class=SimpleBrandonAlgo,
        data=data,
        optimization_metric='total_return',
        train_ratio=0.6,
        validation_ratio=0.2,
        test_ratio=0.2
    )
    
    # Run random search
    results = optimizer.random_search(parameter_ranges, n_iterations=50)
    
    print(f"\n📊 Random Search Results:")
    print(f"   Best Total Return: {results.best_score:.4f}")
    print(f"   Best Parameters: {results.best_params}")
    print(f"   Optimization Time: {results.optimization_time:.1f} seconds")
    print(f"   Total Iterations: {len(results.all_results)}")
    
    # Validate results
    validation_results = optimizer.validate_best_parameters()
    test_results = optimizer.test_final_performance()
    
    return optimizer, results


def compare_optimization_methods():
    """Compare different optimization methods"""
    print(f"\n⚖️  Comparing Optimization Methods...")
    
    # Generate consistent test data
    data = generate_test_data(300)
    
    # Simple parameter ranges for quick comparison
    parameter_ranges = [
        ParameterRange(
            name='rsi_period',
            min_value=10,
            max_value=18,
            step=2,
            param_type='int'
        ),
        ParameterRange(
            name='bullish_threshold',
            min_value=65,
            max_value=75,
            step=5,
            param_type='float'
        ),
        ParameterRange(
            name='bearish_threshold',
            min_value=25,
            max_value=35,
            step=5,
            param_type='float'
        )
    ]
    
    methods_results = {}
    
    # Test Grid Search
    print(f"\n🔍 Testing Grid Search...")
    optimizer_grid = ParameterOptimizer(
        strategy_class=SimpleBrandonAlgo,
        data=data,
        optimization_metric='sharpe_ratio'
    )
    grid_results = optimizer_grid.grid_search(parameter_ranges)
    methods_results['Grid Search'] = {
        'best_score': grid_results.best_score,
        'time': grid_results.optimization_time,
        'params': grid_results.best_params
    }
    
    # Test Random Search
    print(f"\n🎲 Testing Random Search...")
    optimizer_random = ParameterOptimizer(
        strategy_class=SimpleBrandonAlgo,
        data=data,
        optimization_metric='sharpe_ratio'
    )
    random_results = optimizer_random.random_search(parameter_ranges, n_iterations=30)
    methods_results['Random Search'] = {
        'best_score': random_results.best_score,
        'time': random_results.optimization_time,
        'params': random_results.best_params
    }
    
    # Compare results
    print(f"\n📊 Optimization Methods Comparison:")
    print(f"{'Method':<15} {'Best Score':<12} {'Time (s)':<10} {'Parameters'}")
    print(f"{'-'*70}")
    
    for method, results in methods_results.items():
        params_str = str(results['params'])[:40] + "..." if len(str(results['params'])) > 40 else str(results['params'])
        print(f"{method:<15} {results['best_score']:<12.4f} {results['time']:<10.1f} {params_str}")
    
    return methods_results


def test_optimization_robustness():
    """Test optimization robustness with different market conditions"""
    print(f"\n🛡️  Testing Optimization Robustness...")
    
    # Generate data with different market conditions
    market_conditions = {
        'Bull Market': {'drift': 0.002, 'volatility': 0.015},
        'Bear Market': {'drift': -0.001, 'volatility': 0.025},
        'Sideways Market': {'drift': 0.0001, 'volatility': 0.02}
    }
    
    parameter_ranges = [
        ParameterRange(name='rsi_period', min_value=12, max_value=16, step=2, param_type='int'),
        ParameterRange(name='bullish_threshold', min_value=68, max_value=72, step=2, param_type='float')
    ]
    
    robustness_results = {}
    
    for condition_name, params in market_conditions.items():
        print(f"\n📈 Testing in {condition_name}...")
        
        # Generate data for this market condition
        dates = pd.date_range(start=datetime.now() - timedelta(days=200), 
                             end=datetime.now(), freq='D')
        
        np.random.seed(42)  # Consistent seed for comparison
        returns = np.random.normal(params['drift'], params['volatility'], len(dates))
        
        base_price = 50000.0
        cumulative_returns = np.cumsum(returns)
        close_prices = base_price * np.exp(cumulative_returns)
        
        # Create OHLCV data
        data = []
        for i, (date, close) in enumerate(zip(dates, close_prices)):
            vol = abs(returns[i]) * close
            high = close + np.random.uniform(0, vol)
            low = close - np.random.uniform(0, vol)
            open_price = close_prices[i-1] if i > 0 else close
            high = max(high, open_price, close)
            low = min(low, open_price, close)
            volume = 1000000 * np.random.uniform(0.5, 2.0)
            
            data.append({
                'open': open_price, 'high': high, 'low': low, 
                'close': close, 'volume': volume
            })
        
        market_data = pd.DataFrame(data, index=dates)
        
        # Optimize for this market condition
        optimizer = ParameterOptimizer(
            strategy_class=SimpleBrandonAlgo,
            data=market_data,
            optimization_metric='sharpe_ratio'
        )
        
        results = optimizer.grid_search(parameter_ranges, max_combinations=20)
        
        robustness_results[condition_name] = {
            'best_score': results.best_score,
            'best_params': results.best_params
        }
    
    # Analyze robustness
    print(f"\n📊 Robustness Analysis:")
    print(f"{'Market Condition':<15} {'Best Score':<12} {'Parameters'}")
    print(f"{'-'*60}")
    
    for condition, results in robustness_results.items():
        params_str = str(results['best_params'])[:30] + "..." if len(str(results['best_params'])) > 30 else str(results['best_params'])
        print(f"{condition:<15} {results['best_score']:<12.4f} {params_str}")
    
    return robustness_results


def main():
    """Main test function"""
    print("🚀 Parameter Optimization System Test")
    print("=" * 60)
    
    try:
        # Test 1: Grid Search
        grid_optimizer, grid_results = test_grid_search_optimization()
        
        # Test 2: Random Search
        random_optimizer, random_results = test_random_search_optimization()
        
        # Test 3: Method Comparison
        comparison_results = compare_optimization_methods()
        
        # Test 4: Robustness Testing
        robustness_results = test_optimization_robustness()
        
        # Save results
        print(f"\n💾 Saving optimization results...")
        
        # Save grid search results
        grid_results.all_results.to_csv('grid_search_results.csv', index=False)
        
        # Save random search results
        random_results.all_results.to_csv('random_search_results.csv', index=False)
        
        print(f"✅ Results saved to CSV files")
        
        print(f"\n🎯 Optimization System Test Summary:")
        print(f"   ✅ Grid Search: Working")
        print(f"   ✅ Random Search: Working")
        print(f"   ✅ Method Comparison: Working")
        print(f"   ✅ Robustness Testing: Working")
        print(f"   ✅ Parameter Validation: Working")
        print(f"   ✅ Results Export: Working")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 60)
    print("🏁 Parameter optimization test completed successfully!")
    return True


if __name__ == "__main__":
    main()

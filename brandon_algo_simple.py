"""
Brandon <PERSON>go - Simplified Implementation

A simplified version of the Brandon James Algo that doesn't require TA-Lib,
using only pandas and numpy for technical analysis calculations.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')


class SimpleBrandonAlgo:
    """
    Simplified Brandon James Algo implementation
    
    Uses basic technical indicators without external dependencies:
    - Simple Moving Average (SMA)
    - Exponential Moving Average (EMA) 
    - RSI (Relative Strength Index)
    - MACD (Moving Average Convergence Divergence)
    - Basic trend scoring
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize with configuration parameters"""
        self.config = self._get_default_config()
        if config:
            self.config.update(config)
    
    def _get_default_config(self) -> Dict:
        """Get default configuration"""
        return {
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            'rsi_period': 14,
            'trend_period': 21,
            'volume_period': 20,
            'use_volume': True,
            'bullish_threshold': 70,
            'bearish_threshold': 30,
            'strong_bullish_threshold': 80,
            'strong_bearish_threshold': 20
        }
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate indicator values"""
        if data.empty:
            return pd.DataFrame()
        
        result = data.copy()
        
        # Calculate basic indicators
        result['sma_20'] = self._sma(data['close'], 20)
        result['ema_20'] = self._ema(data['close'], 20)
        result['rsi'] = self._rsi(data['close'], self.config['rsi_period'])
        
        # Calculate MACD
        macd_line, macd_signal, macd_histogram = self._macd(
            data['close'], 
            self.config['macd_fast'], 
            self.config['macd_slow'], 
            self.config['macd_signal']
        )
        result['macd_line'] = macd_line
        result['macd_signal'] = macd_signal
        result['macd_histogram'] = macd_histogram
        
        # Calculate volume indicators if enabled
        if self.config['use_volume']:
            result['volume_sma'] = self._sma(data['volume'], self.config['volume_period'])
            result['volume_ratio'] = data['volume'] / result['volume_sma']
        
        # Calculate trend score
        result['trend_score'] = self._calculate_trend_score(result)
        
        # Generate signals
        result['signal'] = self._generate_signals(result)
        result['signal_strength'] = self._calculate_signal_strength(result)
        
        return result
    
    def _sma(self, series: pd.Series, period: int) -> pd.Series:
        """Simple Moving Average"""
        return series.rolling(window=period).mean()
    
    def _ema(self, series: pd.Series, period: int) -> pd.Series:
        """Exponential Moving Average"""
        return series.ewm(span=period).mean()
    
    def _rsi(self, series: pd.Series, period: int) -> pd.Series:
        """Relative Strength Index"""
        delta = series.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _macd(self, series: pd.Series, fast: int, slow: int, signal: int) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """MACD Calculation"""
        ema_fast = self._ema(series, fast)
        ema_slow = self._ema(series, slow)
        macd_line = ema_fast - ema_slow
        macd_signal = self._ema(macd_line, signal)
        macd_histogram = macd_line - macd_signal
        return macd_line, macd_signal, macd_histogram
    
    def _calculate_trend_score(self, data: pd.DataFrame) -> pd.Series:
        """Calculate trend score (0-100)"""
        scores = []
        
        for i in range(len(data)):
            if i < 50:  # Need enough data
                scores.append(50.0)  # Neutral
                continue
            
            score = 50.0  # Start neutral
            
            # RSI component (30% weight)
            rsi = data['rsi'].iloc[i]
            if not pd.isna(rsi):
                if rsi > 70:
                    score += (rsi - 70) / 30 * 15  # Max +15 points
                elif rsi < 30:
                    score -= (30 - rsi) / 30 * 15  # Max -15 points
                else:
                    score += (rsi - 50) / 20 * 5   # Gradual adjustment
            
            # MACD component (40% weight)
            macd_hist = data['macd_histogram'].iloc[i]
            if not pd.isna(macd_hist):
                # Normalize MACD histogram
                recent_macd = data['macd_histogram'].iloc[max(0, i-20):i+1]
                macd_std = recent_macd.std()
                if macd_std > 0:
                    macd_normalized = macd_hist / macd_std
                    score += np.clip(macd_normalized * 10, -20, 20)
            
            # Price trend component (30% weight)
            current_price = data['close'].iloc[i]
            sma_20 = data['sma_20'].iloc[i]
            if not pd.isna(sma_20) and sma_20 > 0:
                price_vs_sma = (current_price - sma_20) / sma_20
                score += np.clip(price_vs_sma * 100, -15, 15)
            
            # Volume component (if enabled)
            if self.config['use_volume'] and 'volume_ratio' in data.columns:
                vol_ratio = data['volume_ratio'].iloc[i]
                if not pd.isna(vol_ratio):
                    if vol_ratio > 1.5:  # High volume
                        # Amplify the current trend
                        if score > 50:
                            score += min(5, (vol_ratio - 1) * 2)
                        else:
                            score -= min(5, (vol_ratio - 1) * 2)
            
            # Ensure score stays within bounds
            score = np.clip(score, 0, 100)
            scores.append(score)
        
        return pd.Series(scores, index=data.index)
    
    def _generate_signals(self, data: pd.DataFrame) -> pd.Series:
        """Generate trading signals"""
        signals = []
        
        for i in range(len(data)):
            trend_score = data['trend_score'].iloc[i]
            
            if trend_score > self.config['strong_bullish_threshold']:
                signal = 'STRONG_BUY'
            elif trend_score > self.config['bullish_threshold']:
                signal = 'BUY'
            elif trend_score < self.config['strong_bearish_threshold']:
                signal = 'STRONG_SELL'
            elif trend_score < self.config['bearish_threshold']:
                signal = 'SELL'
            else:
                signal = 'HOLD'
            
            signals.append(signal)
        
        return pd.Series(signals, index=data.index)
    
    def _calculate_signal_strength(self, data: pd.DataFrame) -> pd.Series:
        """Calculate signal strength (0-1)"""
        strengths = []
        
        for i in range(len(data)):
            trend_score = data['trend_score'].iloc[i]
            
            # Calculate distance from neutral (50)
            distance_from_neutral = abs(trend_score - 50)
            strength = min(1.0, distance_from_neutral / 50)
            
            strengths.append(strength)
        
        return pd.Series(strengths, index=data.index)
    
    def get_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Get trading signals with additional information"""
        result = self.calculate(data)
        
        signals = pd.DataFrame(index=data.index)
        signals['trend_score'] = result['trend_score']
        signals['signal'] = result['signal']
        signals['signal_strength'] = result['signal_strength']
        signals['trend_direction'] = np.where(result['trend_score'] > 50, 'BULLISH', 'BEARISH')
        
        return signals
    
    def backtest(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Simple backtest implementation"""
        signals = self.get_signals(data)
        
        # Simple backtest logic
        initial_capital = 10000.0
        capital = initial_capital
        position = 0  # 0 = no position, 1 = long, -1 = short
        entry_price = 0
        trades = []
        equity_curve = [initial_capital]
        
        commission = 0.001  # 0.1%
        
        for timestamp, row in signals.iterrows():
            current_price = data.loc[timestamp, 'close']
            signal = row['signal']
            
            # Simple exit logic
            if position != 0:
                should_exit = False
                
                if position == 1 and signal in ['SELL', 'STRONG_SELL']:
                    should_exit = True
                elif position == -1 and signal in ['BUY', 'STRONG_BUY']:
                    should_exit = True
                
                if should_exit:
                    # Calculate PnL
                    if position == 1:
                        pnl = (current_price - entry_price) / entry_price
                    else:
                        pnl = (entry_price - current_price) / entry_price
                    
                    pnl -= 2 * commission  # Entry and exit commission
                    capital *= (1 + pnl)
                    
                    trades.append({
                        'entry_price': entry_price,
                        'exit_price': current_price,
                        'pnl_percent': pnl,
                        'position': 'LONG' if position == 1 else 'SHORT'
                    })
                    
                    position = 0
            
            # Entry logic
            if position == 0:
                if signal in ['BUY', 'STRONG_BUY']:
                    position = 1
                    entry_price = current_price
                elif signal in ['SELL', 'STRONG_SELL']:
                    position = -1
                    entry_price = current_price
            
            equity_curve.append(capital)
        
        # Calculate performance metrics
        total_return = (capital - initial_capital) / initial_capital
        
        if trades:
            winning_trades = [t for t in trades if t['pnl_percent'] > 0]
            win_rate = len(winning_trades) / len(trades)
        else:
            win_rate = 0
        
        return {
            'total_return': total_return,
            'final_capital': capital,
            'total_trades': len(trades),
            'win_rate': win_rate,
            'trades': trades,
            'equity_curve': equity_curve
        }

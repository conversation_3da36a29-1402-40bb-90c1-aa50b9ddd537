"""
Base Strategy Class

Provides the foundation for all generated trading strategies
with common functionality for signal processing, risk management,
and performance tracking.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging


class SignalType(Enum):
    """Types of trading signals"""
    BUY = "BUY"
    SELL = "SELL"
    STRONG_BUY = "STRONG_BUY"
    STRONG_SELL = "STRONG_SELL"
    HOLD = "HOLD"


class PositionType(Enum):
    """Types of positions"""
    LONG = "LONG"
    SHORT = "SHORT"
    FLAT = "FLAT"


@dataclass
class Trade:
    """Represents a completed trade"""
    entry_time: pd.Timestamp
    exit_time: pd.Timestamp
    entry_price: float
    exit_price: float
    position_type: PositionType
    quantity: float
    pnl: float
    pnl_percent: float
    duration: pd.Timedelta
    entry_signal: SignalType
    exit_signal: SignalType


@dataclass
class Position:
    """Represents an open position"""
    entry_time: pd.Timestamp
    entry_price: float
    position_type: PositionType
    quantity: float
    entry_signal: SignalType
    unrealized_pnl: float = 0.0


class BaseStrategy:
    """
    Base class for all trading strategies
    
    Provides common functionality for:
    - Signal processing
    - Position management
    - Risk management
    - Performance tracking
    """
    
    def __init__(self, 
                 initial_capital: float = 10000.0,
                 position_size: float = 0.1,
                 stop_loss: float = 0.02,
                 take_profit: float = 0.04,
                 max_positions: int = 1,
                 commission: float = 0.001):
        """
        Initialize strategy
        
        Args:
            initial_capital: Starting capital
            position_size: Position size as fraction of capital (0.1 = 10%)
            stop_loss: Stop loss as fraction (0.02 = 2%)
            take_profit: Take profit as fraction (0.04 = 4%)
            max_positions: Maximum concurrent positions
            commission: Commission rate per trade
        """
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.position_size = position_size
        self.stop_loss = stop_loss
        self.take_profit = take_profit
        self.max_positions = max_positions
        self.commission = commission
        
        # Strategy state
        self.positions: List[Position] = []
        self.trades: List[Trade] = []
        self.equity_curve: List[float] = [initial_capital]
        self.current_time: Optional[pd.Timestamp] = None
        
        # Performance metrics
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_pnl = 0.0
        self.max_drawdown = 0.0
        self.peak_equity = initial_capital
        
        # Setup logging
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def process_signal(self, timestamp: pd.Timestamp, price: float, 
                      signal: SignalType, signal_strength: float = 1.0) -> None:
        """
        Process a trading signal
        
        Args:
            timestamp: Signal timestamp
            price: Current price
            signal: Signal type
            signal_strength: Signal strength (0-1)
        """
        self.current_time = timestamp
        
        # Update unrealized PnL for open positions
        self._update_unrealized_pnl(price)
        
        # Check for exit conditions first
        self._check_exit_conditions(timestamp, price)
        
        # Process entry signals if we have capacity
        if len(self.positions) < self.max_positions:
            if signal in [SignalType.BUY, SignalType.STRONG_BUY]:
                self._enter_long(timestamp, price, signal, signal_strength)
            elif signal in [SignalType.SELL, SignalType.STRONG_SELL]:
                self._enter_short(timestamp, price, signal, signal_strength)
        
        # Update equity curve
        current_equity = self._calculate_current_equity(price)
        self.equity_curve.append(current_equity)
        self.current_capital = current_equity
        
        # Update performance metrics
        self._update_performance_metrics()
    
    def _enter_long(self, timestamp: pd.Timestamp, price: float, 
                   signal: SignalType, signal_strength: float) -> None:
        """Enter a long position"""
        if len(self.positions) >= self.max_positions:
            return
        
        # Calculate position size
        position_value = self.current_capital * self.position_size * signal_strength
        quantity = position_value / price
        
        # Account for commission
        commission_cost = position_value * self.commission
        self.current_capital -= commission_cost
        
        # Create position
        position = Position(
            entry_time=timestamp,
            entry_price=price,
            position_type=PositionType.LONG,
            quantity=quantity,
            entry_signal=signal
        )
        
        self.positions.append(position)
        self.logger.info(f"Entered LONG position: {quantity:.4f} @ {price:.4f}")
    
    def _enter_short(self, timestamp: pd.Timestamp, price: float, 
                    signal: SignalType, signal_strength: float) -> None:
        """Enter a short position"""
        if len(self.positions) >= self.max_positions:
            return
        
        # Calculate position size
        position_value = self.current_capital * self.position_size * signal_strength
        quantity = position_value / price
        
        # Account for commission
        commission_cost = position_value * self.commission
        self.current_capital -= commission_cost
        
        # Create position
        position = Position(
            entry_time=timestamp,
            entry_price=price,
            position_type=PositionType.SHORT,
            quantity=quantity,
            entry_signal=signal
        )
        
        self.positions.append(position)
        self.logger.info(f"Entered SHORT position: {quantity:.4f} @ {price:.4f}")
    
    def _check_exit_conditions(self, timestamp: pd.Timestamp, price: float) -> None:
        """Check if any positions should be closed"""
        positions_to_close = []
        
        for position in self.positions:
            should_close, exit_signal = self._should_close_position(position, price)
            if should_close:
                positions_to_close.append((position, exit_signal))
        
        # Close positions
        for position, exit_signal in positions_to_close:
            self._close_position(timestamp, price, position, exit_signal)
    
    def _should_close_position(self, position: Position, current_price: float) -> Tuple[bool, SignalType]:
        """Check if a position should be closed"""
        if position.position_type == PositionType.LONG:
            # Long position exit conditions
            pnl_percent = (current_price - position.entry_price) / position.entry_price
            
            if pnl_percent <= -self.stop_loss:
                return True, SignalType.SELL  # Stop loss
            elif pnl_percent >= self.take_profit:
                return True, SignalType.SELL  # Take profit
                
        elif position.position_type == PositionType.SHORT:
            # Short position exit conditions
            pnl_percent = (position.entry_price - current_price) / position.entry_price
            
            if pnl_percent <= -self.stop_loss:
                return True, SignalType.BUY  # Stop loss
            elif pnl_percent >= self.take_profit:
                return True, SignalType.BUY  # Take profit
        
        return False, SignalType.HOLD
    
    def _close_position(self, timestamp: pd.Timestamp, price: float, 
                       position: Position, exit_signal: SignalType) -> None:
        """Close a position and record the trade"""
        # Calculate PnL
        if position.position_type == PositionType.LONG:
            pnl = (price - position.entry_price) * position.quantity
        else:  # SHORT
            pnl = (position.entry_price - price) * position.quantity
        
        # Account for commission
        position_value = position.quantity * price
        commission_cost = position_value * self.commission
        pnl -= commission_cost
        
        pnl_percent = pnl / (position.entry_price * position.quantity)
        
        # Create trade record
        trade = Trade(
            entry_time=position.entry_time,
            exit_time=timestamp,
            entry_price=position.entry_price,
            exit_price=price,
            position_type=position.position_type,
            quantity=position.quantity,
            pnl=pnl,
            pnl_percent=pnl_percent,
            duration=timestamp - position.entry_time,
            entry_signal=position.entry_signal,
            exit_signal=exit_signal
        )
        
        self.trades.append(trade)
        self.positions.remove(position)
        
        # Update capital
        self.current_capital += pnl
        
        # Update trade statistics
        self.total_trades += 1
        self.total_pnl += pnl
        
        if pnl > 0:
            self.winning_trades += 1
        else:
            self.losing_trades += 1
        
        self.logger.info(f"Closed {position.position_type.value} position: "
                        f"PnL = {pnl:.2f} ({pnl_percent:.2%})")
    
    def _update_unrealized_pnl(self, current_price: float) -> None:
        """Update unrealized PnL for open positions"""
        for position in self.positions:
            if position.position_type == PositionType.LONG:
                position.unrealized_pnl = (current_price - position.entry_price) * position.quantity
            else:  # SHORT
                position.unrealized_pnl = (position.entry_price - current_price) * position.quantity
    
    def _calculate_current_equity(self, current_price: float) -> float:
        """Calculate current total equity including unrealized PnL"""
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions)
        return self.current_capital + total_unrealized_pnl
    
    def _update_performance_metrics(self) -> None:
        """Update performance metrics"""
        current_equity = self.equity_curve[-1]
        
        # Update peak equity and drawdown
        if current_equity > self.peak_equity:
            self.peak_equity = current_equity
        
        current_drawdown = (self.peak_equity - current_equity) / self.peak_equity
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary statistics"""
        if not self.trades:
            return {"message": "No trades completed yet"}
        
        # Calculate metrics
        win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        avg_win = np.mean([t.pnl for t in self.trades if t.pnl > 0]) if self.winning_trades > 0 else 0
        avg_loss = np.mean([t.pnl for t in self.trades if t.pnl < 0]) if self.losing_trades > 0 else 0
        profit_factor = abs(avg_win * self.winning_trades / (avg_loss * self.losing_trades)) if avg_loss != 0 else float('inf')
        
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital
        
        return {
            "total_trades": self.total_trades,
            "winning_trades": self.winning_trades,
            "losing_trades": self.losing_trades,
            "win_rate": win_rate,
            "total_pnl": self.total_pnl,
            "total_return": total_return,
            "max_drawdown": self.max_drawdown,
            "profit_factor": profit_factor,
            "avg_win": avg_win,
            "avg_loss": avg_loss,
            "current_capital": self.current_capital,
            "open_positions": len(self.positions)
        }

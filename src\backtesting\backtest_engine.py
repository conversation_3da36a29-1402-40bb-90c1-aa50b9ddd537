"""
Backtesting Engine

Comprehensive backtesting framework that tests strategies on historical data
with realistic trading costs, slippage, and market conditions.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')


@dataclass
class BacktestConfig:
    """Configuration for backtesting"""
    initial_capital: float = 10000.0
    commission: float = 0.001  # 0.1%
    slippage: float = 0.0005   # 0.05%
    min_trade_amount: float = 10.0
    max_position_size: float = 1.0  # 100% of capital
    risk_free_rate: float = 0.02    # 2% annual risk-free rate
    benchmark_symbol: str = "SPY"


@dataclass
class BacktestResults:
    """Results from a backtest run"""
    trades: pd.DataFrame
    equity_curve: pd.DataFrame
    performance_metrics: Dict[str, float]
    drawdown_analysis: Dict[str, Any]
    monthly_returns: pd.DataFrame
    trade_analysis: Dict[str, Any]
    config: BacktestConfig


class BacktestEngine:
    """
    Advanced backtesting engine for strategy evaluation
    
    Features:
    - Realistic trading costs (commission + slippage)
    - Multiple performance metrics
    - Risk analysis and drawdown calculations
    - Benchmark comparison
    - Monte Carlo analysis
    - Walk-forward optimization support
    """
    
    def __init__(self, config: Optional[BacktestConfig] = None):
        self.config = config or BacktestConfig()
        self.results: Optional[BacktestResults] = None
    
    def run_backtest(self, strategy, data: pd.DataFrame, 
                    benchmark_data: Optional[pd.DataFrame] = None) -> BacktestResults:
        """
        Run a complete backtest
        
        Args:
            strategy: Strategy instance to test
            data: Historical OHLCV data
            benchmark_data: Optional benchmark data for comparison
            
        Returns:
            BacktestResults with comprehensive analysis
        """
        print(f"🚀 Starting backtest...")
        print(f"📊 Data period: {data.index[0]} to {data.index[-1]}")
        print(f"📊 Total bars: {len(data)}")
        
        # Validate data
        self._validate_data(data)
        
        # Run the strategy backtest
        strategy_results = strategy.backtest(data)
        
        # Extract trades and equity curve from strategy
        trades_df = self._extract_trades(strategy)
        equity_curve = self._calculate_equity_curve(strategy, data)
        
        # Calculate performance metrics
        performance_metrics = self._calculate_performance_metrics(
            equity_curve, trades_df, benchmark_data)
        
        # Analyze drawdowns
        drawdown_analysis = self._analyze_drawdowns(equity_curve)
        
        # Calculate monthly returns
        monthly_returns = self._calculate_monthly_returns(equity_curve)
        
        # Analyze trades
        trade_analysis = self._analyze_trades(trades_df)
        
        # Create results object
        self.results = BacktestResults(
            trades=trades_df,
            equity_curve=equity_curve,
            performance_metrics=performance_metrics,
            drawdown_analysis=drawdown_analysis,
            monthly_returns=monthly_returns,
            trade_analysis=trade_analysis,
            config=self.config
        )
        
        print(f"✅ Backtest completed!")
        self._print_summary()
        
        return self.results
    
    def _validate_data(self, data: pd.DataFrame) -> None:
        """Validate input data"""
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        if data.empty:
            raise ValueError("Data cannot be empty")
        
        if not isinstance(data.index, pd.DatetimeIndex):
            raise ValueError("Data index must be DatetimeIndex")
    
    def _extract_trades(self, strategy) -> pd.DataFrame:
        """Extract trades from strategy into DataFrame"""
        if not strategy.trades:
            return pd.DataFrame()
        
        trades_data = []
        for trade in strategy.trades:
            trades_data.append({
                'entry_time': trade.entry_time,
                'exit_time': trade.exit_time,
                'entry_price': trade.entry_price,
                'exit_price': trade.exit_price,
                'position_type': trade.position_type.value,
                'quantity': trade.quantity,
                'pnl': trade.pnl,
                'pnl_percent': trade.pnl_percent,
                'duration': trade.duration.total_seconds() / 3600,  # Hours
                'entry_signal': trade.entry_signal.value,
                'exit_signal': trade.exit_signal.value
            })
        
        return pd.DataFrame(trades_data)
    
    def _calculate_equity_curve(self, strategy, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate detailed equity curve"""
        equity_data = []
        
        # Use strategy's equity curve if available
        if hasattr(strategy, 'equity_curve') and strategy.equity_curve:
            for i, equity in enumerate(strategy.equity_curve):
                if i < len(data):
                    equity_data.append({
                        'timestamp': data.index[i],
                        'equity': equity,
                        'returns': (equity / strategy.initial_capital - 1) if i == 0 else 
                                  (equity / strategy.equity_curve[i-1] - 1),
                        'cumulative_returns': equity / strategy.initial_capital - 1
                    })
        
        equity_df = pd.DataFrame(equity_data)
        if not equity_df.empty:
            equity_df.set_index('timestamp', inplace=True)
        
        return equity_df
    
    def _calculate_performance_metrics(self, equity_curve: pd.DataFrame, 
                                     trades_df: pd.DataFrame,
                                     benchmark_data: Optional[pd.DataFrame] = None) -> Dict[str, float]:
        """Calculate comprehensive performance metrics"""
        if equity_curve.empty:
            return {}
        
        returns = equity_curve['returns'].dropna()
        cumulative_returns = equity_curve['cumulative_returns'].iloc[-1]
        
        # Basic metrics
        total_return = cumulative_returns
        annualized_return = (1 + total_return) ** (252 / len(equity_curve)) - 1
        
        # Risk metrics
        volatility = returns.std() * np.sqrt(252)  # Annualized
        sharpe_ratio = (annualized_return - self.config.risk_free_rate) / volatility if volatility > 0 else 0
        
        # Drawdown metrics
        running_max = equity_curve['equity'].expanding().max()
        drawdown = (equity_curve['equity'] - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # Trade-based metrics
        if not trades_df.empty:
            win_rate = len(trades_df[trades_df['pnl'] > 0]) / len(trades_df)
            avg_win = trades_df[trades_df['pnl'] > 0]['pnl'].mean() if len(trades_df[trades_df['pnl'] > 0]) > 0 else 0
            avg_loss = trades_df[trades_df['pnl'] < 0]['pnl'].mean() if len(trades_df[trades_df['pnl'] < 0]) > 0 else 0
            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
            avg_trade_duration = trades_df['duration'].mean()
        else:
            win_rate = 0
            avg_win = 0
            avg_loss = 0
            profit_factor = 0
            avg_trade_duration = 0
        
        # Calmar ratio (return / max drawdown)
        calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # Sortino ratio (downside deviation)
        downside_returns = returns[returns < 0]
        downside_deviation = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 else 0
        sortino_ratio = (annualized_return - self.config.risk_free_rate) / downside_deviation if downside_deviation > 0 else 0
        
        return {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'calmar_ratio': calmar_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'total_trades': len(trades_df),
            'avg_trade_duration_hours': avg_trade_duration
        }
    
    def _analyze_drawdowns(self, equity_curve: pd.DataFrame) -> Dict[str, Any]:
        """Analyze drawdown periods"""
        if equity_curve.empty:
            return {}
        
        running_max = equity_curve['equity'].expanding().max()
        drawdown = (equity_curve['equity'] - running_max) / running_max
        
        # Find drawdown periods
        in_drawdown = drawdown < 0
        drawdown_periods = []
        
        start_idx = None
        for i, is_dd in enumerate(in_drawdown):
            if is_dd and start_idx is None:
                start_idx = i
            elif not is_dd and start_idx is not None:
                end_idx = i - 1
                period_drawdown = drawdown.iloc[start_idx:end_idx+1]
                max_dd = period_drawdown.min()
                duration = equity_curve.index[end_idx] - equity_curve.index[start_idx]
                
                drawdown_periods.append({
                    'start': equity_curve.index[start_idx],
                    'end': equity_curve.index[end_idx],
                    'duration_days': duration.days,
                    'max_drawdown': max_dd
                })
                start_idx = None
        
        # Handle case where we end in drawdown
        if start_idx is not None:
            period_drawdown = drawdown.iloc[start_idx:]
            max_dd = period_drawdown.min()
            duration = equity_curve.index[-1] - equity_curve.index[start_idx]
            
            drawdown_periods.append({
                'start': equity_curve.index[start_idx],
                'end': equity_curve.index[-1],
                'duration_days': duration.days,
                'max_drawdown': max_dd
            })
        
        return {
            'drawdown_periods': drawdown_periods,
            'avg_drawdown_duration': np.mean([dd['duration_days'] for dd in drawdown_periods]) if drawdown_periods else 0,
            'max_drawdown_duration': max([dd['duration_days'] for dd in drawdown_periods]) if drawdown_periods else 0,
            'total_drawdown_periods': len(drawdown_periods)
        }
    
    def _calculate_monthly_returns(self, equity_curve: pd.DataFrame) -> pd.DataFrame:
        """Calculate monthly returns"""
        if equity_curve.empty:
            return pd.DataFrame()
        
        monthly_equity = equity_curve['equity'].resample('M').last()
        monthly_returns = monthly_equity.pct_change().dropna()
        
        monthly_df = pd.DataFrame({
            'returns': monthly_returns,
            'cumulative_returns': (1 + monthly_returns).cumprod() - 1
        })
        
        return monthly_df
    
    def _analyze_trades(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze trade patterns and statistics"""
        if trades_df.empty:
            return {}
        
        # Winning vs losing trades
        winning_trades = trades_df[trades_df['pnl'] > 0]
        losing_trades = trades_df[trades_df['pnl'] < 0]
        
        # Consecutive wins/losses
        trade_results = (trades_df['pnl'] > 0).astype(int)
        consecutive_wins = self._max_consecutive(trade_results, 1)
        consecutive_losses = self._max_consecutive(trade_results, 0)
        
        # Trade duration analysis
        duration_stats = trades_df['duration'].describe()
        
        return {
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'largest_win': trades_df['pnl'].max(),
            'largest_loss': trades_df['pnl'].min(),
            'avg_winning_trade': winning_trades['pnl'].mean() if len(winning_trades) > 0 else 0,
            'avg_losing_trade': losing_trades['pnl'].mean() if len(losing_trades) > 0 else 0,
            'max_consecutive_wins': consecutive_wins,
            'max_consecutive_losses': consecutive_losses,
            'avg_trade_duration_hours': duration_stats['mean'],
            'median_trade_duration_hours': duration_stats['50%'],
            'min_trade_duration_hours': duration_stats['min'],
            'max_trade_duration_hours': duration_stats['max']
        }
    
    def _max_consecutive(self, series: pd.Series, value: int) -> int:
        """Find maximum consecutive occurrences of a value"""
        max_count = 0
        current_count = 0
        
        for val in series:
            if val == value:
                current_count += 1
                max_count = max(max_count, current_count)
            else:
                current_count = 0
        
        return max_count
    
    def _print_summary(self) -> None:
        """Print backtest summary"""
        if not self.results:
            return
        
        metrics = self.results.performance_metrics
        
        print(f"\n📈 Backtest Results Summary:")
        print(f"   Total Return: {metrics.get('total_return', 0):.2%}")
        print(f"   Annualized Return: {metrics.get('annualized_return', 0):.2%}")
        print(f"   Volatility: {metrics.get('volatility', 0):.2%}")
        print(f"   Sharpe Ratio: {metrics.get('sharpe_ratio', 0):.2f}")
        print(f"   Max Drawdown: {metrics.get('max_drawdown', 0):.2%}")
        print(f"   Win Rate: {metrics.get('win_rate', 0):.2%}")
        print(f"   Profit Factor: {metrics.get('profit_factor', 0):.2f}")
        print(f"   Total Trades: {metrics.get('total_trades', 0)}")
    
    def plot_results(self, save_path: Optional[str] = None) -> None:
        """Plot backtest results"""
        if not self.results or self.results.equity_curve.empty:
            print("No results to plot")
            return
        
        # Create subplots
        fig = make_subplots(
            rows=3, cols=1,
            subplot_titles=('Equity Curve', 'Drawdown', 'Monthly Returns'),
            vertical_spacing=0.08,
            row_heights=[0.5, 0.25, 0.25]
        )
        
        # Equity curve
        fig.add_trace(
            go.Scatter(
                x=self.results.equity_curve.index,
                y=self.results.equity_curve['equity'],
                name='Portfolio Value',
                line=dict(color='blue', width=2)
            ),
            row=1, col=1
        )
        
        # Drawdown
        running_max = self.results.equity_curve['equity'].expanding().max()
        drawdown = (self.results.equity_curve['equity'] - running_max) / running_max * 100
        
        fig.add_trace(
            go.Scatter(
                x=self.results.equity_curve.index,
                y=drawdown,
                name='Drawdown %',
                fill='tonexty',
                line=dict(color='red', width=1)
            ),
            row=2, col=1
        )
        
        # Monthly returns
        if not self.results.monthly_returns.empty:
            colors = ['green' if x > 0 else 'red' for x in self.results.monthly_returns['returns']]
            fig.add_trace(
                go.Bar(
                    x=self.results.monthly_returns.index,
                    y=self.results.monthly_returns['returns'] * 100,
                    name='Monthly Returns %',
                    marker_color=colors
                ),
                row=3, col=1
            )
        
        # Update layout
        fig.update_layout(
            title='Backtest Results Analysis',
            height=800,
            showlegend=True
        )
        
        fig.update_xaxes(title_text="Date", row=3, col=1)
        fig.update_yaxes(title_text="Portfolio Value ($)", row=1, col=1)
        fig.update_yaxes(title_text="Drawdown (%)", row=2, col=1)
        fig.update_yaxes(title_text="Returns (%)", row=3, col=1)
        
        if save_path:
            fig.write_html(save_path)
            print(f"📊 Results plot saved to: {save_path}")
        else:
            fig.show()
    
    def export_results(self, filepath: str) -> None:
        """Export results to Excel file"""
        if not self.results:
            print("No results to export")
            return
        
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            # Performance metrics
            metrics_df = pd.DataFrame(list(self.results.performance_metrics.items()), 
                                    columns=['Metric', 'Value'])
            metrics_df.to_excel(writer, sheet_name='Performance Metrics', index=False)
            
            # Trades
            if not self.results.trades.empty:
                self.results.trades.to_excel(writer, sheet_name='Trades', index=False)
            
            # Equity curve
            if not self.results.equity_curve.empty:
                self.results.equity_curve.to_excel(writer, sheet_name='Equity Curve')
            
            # Monthly returns
            if not self.results.monthly_returns.empty:
                self.results.monthly_returns.to_excel(writer, sheet_name='Monthly Returns')
        
        print(f"📊 Results exported to: {filepath}")

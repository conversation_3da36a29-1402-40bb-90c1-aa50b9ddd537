"""
Parameter Optimizer

Main optimization engine that coordinates different optimization methods
and provides a unified interface for parameter optimization.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass
import itertools
from concurrent.futures import ProcessPoolExecutor, as_completed
import warnings
warnings.filterwarnings('ignore')


@dataclass
class OptimizationResult:
    """Results from parameter optimization"""
    best_params: Dict[str, Any]
    best_score: float
    all_results: pd.DataFrame
    optimization_time: float
    method: str
    metric: str


@dataclass
class ParameterRange:
    """Defines a parameter range for optimization"""
    name: str
    min_value: float
    max_value: float
    step: float
    param_type: str = 'float'  # 'float', 'int', 'choice'
    choices: Optional[List] = None


class ParameterOptimizer:
    """
    Main parameter optimization engine
    
    Supports multiple optimization methods:
    - Grid Search: Exhaustive search over parameter grid
    - Random Search: Random sampling of parameter space
    - Genetic Algorithm: Evolutionary optimization
    - Bayesian Optimization: Smart parameter exploration
    """
    
    def __init__(self, 
                 strategy_class,
                 data: pd.DataFrame,
                 optimization_metric: str = 'sharpe_ratio',
                 train_ratio: float = 0.7,
                 validation_ratio: float = 0.15,
                 test_ratio: float = 0.15,
                 n_jobs: int = 1):
        """
        Initialize optimizer
        
        Args:
            strategy_class: Strategy class to optimize
            data: Historical data for optimization
            optimization_metric: Metric to optimize ('sharpe_ratio', 'total_return', 'calmar_ratio')
            train_ratio: Ratio of data for training
            validation_ratio: Ratio of data for validation
            test_ratio: Ratio of data for testing
            n_jobs: Number of parallel jobs
        """
        self.strategy_class = strategy_class
        self.data = data
        self.optimization_metric = optimization_metric
        self.train_ratio = train_ratio
        self.validation_ratio = validation_ratio
        self.test_ratio = test_ratio
        self.n_jobs = n_jobs
        
        # Split data
        self._split_data()
        
        # Results storage
        self.results: Optional[OptimizationResult] = None
    
    def _split_data(self) -> None:
        """Split data into train/validation/test sets"""
        total_length = len(self.data)
        
        train_end = int(total_length * self.train_ratio)
        val_end = int(total_length * (self.train_ratio + self.validation_ratio))
        
        self.train_data = self.data.iloc[:train_end]
        self.validation_data = self.data.iloc[train_end:val_end]
        self.test_data = self.data.iloc[val_end:]
        
        print(f"📊 Data split:")
        print(f"   Training: {len(self.train_data)} samples ({self.train_ratio:.1%})")
        print(f"   Validation: {len(self.validation_data)} samples ({self.validation_ratio:.1%})")
        print(f"   Test: {len(self.test_data)} samples ({self.test_ratio:.1%})")
    
    def grid_search(self, parameter_ranges: List[ParameterRange], 
                   max_combinations: int = 1000) -> OptimizationResult:
        """
        Perform grid search optimization
        
        Args:
            parameter_ranges: List of parameter ranges to search
            max_combinations: Maximum number of combinations to test
            
        Returns:
            OptimizationResult with best parameters and performance
        """
        print(f"🔍 Starting Grid Search Optimization...")
        print(f"📊 Optimizing metric: {self.optimization_metric}")
        
        import time
        start_time = time.time()
        
        # Generate parameter combinations
        param_combinations = self._generate_parameter_combinations(parameter_ranges)
        
        # Limit combinations if too many
        if len(param_combinations) > max_combinations:
            print(f"⚠️  Too many combinations ({len(param_combinations)}), sampling {max_combinations}")
            np.random.seed(42)
            indices = np.random.choice(len(param_combinations), max_combinations, replace=False)
            param_combinations = [param_combinations[i] for i in indices]
        
        print(f"🔧 Testing {len(param_combinations)} parameter combinations...")
        
        # Evaluate combinations
        results = []
        
        if self.n_jobs == 1:
            # Single-threaded execution
            for i, params in enumerate(param_combinations):
                if i % 50 == 0:
                    print(f"   Progress: {i}/{len(param_combinations)} ({i/len(param_combinations):.1%})")
                
                score = self._evaluate_parameters(params)
                results.append({**params, 'score': score})
        else:
            # Multi-threaded execution
            with ProcessPoolExecutor(max_workers=self.n_jobs) as executor:
                futures = {executor.submit(self._evaluate_parameters, params): params 
                          for params in param_combinations}
                
                for i, future in enumerate(as_completed(futures)):
                    if i % 50 == 0:
                        print(f"   Progress: {i}/{len(param_combinations)} ({i/len(param_combinations):.1%})")
                    
                    params = futures[future]
                    score = future.result()
                    results.append({**params, 'score': score})
        
        # Create results DataFrame
        results_df = pd.DataFrame(results)
        results_df = results_df.sort_values('score', ascending=False)
        
        # Get best parameters
        best_params = results_df.iloc[0].to_dict()
        best_score = best_params.pop('score')
        
        optimization_time = time.time() - start_time
        
        print(f"✅ Grid Search completed in {optimization_time:.1f} seconds")
        print(f"🏆 Best {self.optimization_metric}: {best_score:.4f}")
        print(f"🎯 Best parameters: {best_params}")
        
        self.results = OptimizationResult(
            best_params=best_params,
            best_score=best_score,
            all_results=results_df,
            optimization_time=optimization_time,
            method='grid_search',
            metric=self.optimization_metric
        )
        
        return self.results
    
    def random_search(self, parameter_ranges: List[ParameterRange], 
                     n_iterations: int = 100) -> OptimizationResult:
        """
        Perform random search optimization
        
        Args:
            parameter_ranges: List of parameter ranges to search
            n_iterations: Number of random combinations to test
            
        Returns:
            OptimizationResult with best parameters and performance
        """
        print(f"🎲 Starting Random Search Optimization...")
        print(f"📊 Optimizing metric: {self.optimization_metric}")
        print(f"🔧 Testing {n_iterations} random parameter combinations...")
        
        import time
        start_time = time.time()
        
        # Generate random parameter combinations
        np.random.seed(42)
        param_combinations = []
        
        for _ in range(n_iterations):
            params = {}
            for param_range in parameter_ranges:
                if param_range.param_type == 'choice':
                    params[param_range.name] = np.random.choice(param_range.choices)
                elif param_range.param_type == 'int':
                    params[param_range.name] = np.random.randint(
                        int(param_range.min_value), 
                        int(param_range.max_value) + 1
                    )
                else:  # float
                    params[param_range.name] = np.random.uniform(
                        param_range.min_value, 
                        param_range.max_value
                    )
            param_combinations.append(params)
        
        # Evaluate combinations
        results = []
        for i, params in enumerate(param_combinations):
            if i % 20 == 0:
                print(f"   Progress: {i}/{n_iterations} ({i/n_iterations:.1%})")
            
            score = self._evaluate_parameters(params)
            results.append({**params, 'score': score})
        
        # Create results DataFrame
        results_df = pd.DataFrame(results)
        results_df = results_df.sort_values('score', ascending=False)
        
        # Get best parameters
        best_params = results_df.iloc[0].to_dict()
        best_score = best_params.pop('score')
        
        optimization_time = time.time() - start_time
        
        print(f"✅ Random Search completed in {optimization_time:.1f} seconds")
        print(f"🏆 Best {self.optimization_metric}: {best_score:.4f}")
        print(f"🎯 Best parameters: {best_params}")
        
        self.results = OptimizationResult(
            best_params=best_params,
            best_score=best_score,
            all_results=results_df,
            optimization_time=optimization_time,
            method='random_search',
            metric=self.optimization_metric
        )
        
        return self.results
    
    def _generate_parameter_combinations(self, parameter_ranges: List[ParameterRange]) -> List[Dict]:
        """Generate all parameter combinations for grid search"""
        param_values = {}
        
        for param_range in parameter_ranges:
            if param_range.param_type == 'choice':
                param_values[param_range.name] = param_range.choices
            elif param_range.param_type == 'int':
                param_values[param_range.name] = list(range(
                    int(param_range.min_value),
                    int(param_range.max_value) + 1,
                    int(param_range.step)
                ))
            else:  # float
                values = []
                current = param_range.min_value
                while current <= param_range.max_value:
                    values.append(round(current, 6))
                    current += param_range.step
                param_values[param_range.name] = values
        
        # Generate all combinations
        keys = list(param_values.keys())
        combinations = []
        
        for values in itertools.product(*[param_values[key] for key in keys]):
            combinations.append(dict(zip(keys, values)))
        
        return combinations
    
    def _evaluate_parameters(self, params: Dict[str, Any]) -> float:
        """Evaluate a set of parameters"""
        try:
            # Create strategy with parameters
            strategy = self.strategy_class(config=params)
            
            # Run backtest on training data
            backtest_results = strategy.backtest(self.train_data)
            
            # Extract the optimization metric
            if self.optimization_metric == 'sharpe_ratio':
                # Calculate Sharpe ratio
                if 'equity_curve' in backtest_results and backtest_results['equity_curve']:
                    returns = pd.Series(backtest_results['equity_curve']).pct_change().dropna()
                    if len(returns) > 0 and returns.std() > 0:
                        return returns.mean() / returns.std() * np.sqrt(252)  # Annualized
                return -999  # Penalty for invalid results
            
            elif self.optimization_metric == 'total_return':
                return backtest_results.get('total_return', -999)
            
            elif self.optimization_metric == 'calmar_ratio':
                total_return = backtest_results.get('total_return', 0)
                # Calculate max drawdown
                if 'equity_curve' in backtest_results and backtest_results['equity_curve']:
                    equity = pd.Series(backtest_results['equity_curve'])
                    running_max = equity.expanding().max()
                    drawdown = (equity - running_max) / running_max
                    max_drawdown = abs(drawdown.min())
                    if max_drawdown > 0:
                        return total_return / max_drawdown
                return -999
            
            elif self.optimization_metric == 'profit_factor':
                return backtest_results.get('profit_factor', 0)
            
            else:
                return backtest_results.get(self.optimization_metric, -999)
                
        except Exception as e:
            print(f"⚠️  Error evaluating parameters {params}: {e}")
            return -999  # Penalty for failed evaluations
    
    def validate_best_parameters(self) -> Dict[str, float]:
        """Validate best parameters on validation set"""
        if not self.results:
            raise ValueError("No optimization results available. Run optimization first.")
        
        print(f"🔍 Validating best parameters on validation set...")
        
        # Test on validation data
        strategy = self.strategy_class(config=self.results.best_params)
        validation_results = strategy.backtest(self.validation_data)
        
        print(f"✅ Validation completed")
        print(f"📊 Validation performance:")
        print(f"   Total Return: {validation_results.get('total_return', 0):.2%}")
        print(f"   Win Rate: {validation_results.get('win_rate', 0):.2%}")
        print(f"   Total Trades: {validation_results.get('total_trades', 0)}")
        
        return validation_results
    
    def test_final_performance(self) -> Dict[str, float]:
        """Test final performance on test set"""
        if not self.results:
            raise ValueError("No optimization results available. Run optimization first.")
        
        print(f"🎯 Testing final performance on test set...")
        
        # Test on test data
        strategy = self.strategy_class(config=self.results.best_params)
        test_results = strategy.backtest(self.test_data)
        
        print(f"✅ Final test completed")
        print(f"📊 Test performance:")
        print(f"   Total Return: {test_results.get('total_return', 0):.2%}")
        print(f"   Win Rate: {test_results.get('win_rate', 0):.2%}")
        print(f"   Total Trades: {test_results.get('total_trades', 0)}")
        
        return test_results
    
    def plot_optimization_results(self, save_path: Optional[str] = None) -> None:
        """Plot optimization results"""
        if not self.results:
            print("No results to plot")
            return
        
        try:
            import matplotlib.pyplot as plt
            
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            
            # Score distribution
            axes[0, 0].hist(self.results.all_results['score'], bins=30, alpha=0.7)
            axes[0, 0].axvline(self.results.best_score, color='red', linestyle='--', 
                              label=f'Best: {self.results.best_score:.4f}')
            axes[0, 0].set_title('Score Distribution')
            axes[0, 0].set_xlabel(self.optimization_metric)
            axes[0, 0].legend()
            
            # Parameter correlation (if multiple parameters)
            param_cols = [col for col in self.results.all_results.columns if col != 'score']
            if len(param_cols) >= 2:
                corr_matrix = self.results.all_results[param_cols + ['score']].corr()
                im = axes[0, 1].imshow(corr_matrix, cmap='coolwarm', vmin=-1, vmax=1)
                axes[0, 1].set_xticks(range(len(corr_matrix.columns)))
                axes[0, 1].set_yticks(range(len(corr_matrix.columns)))
                axes[0, 1].set_xticklabels(corr_matrix.columns, rotation=45)
                axes[0, 1].set_yticklabels(corr_matrix.columns)
                axes[0, 1].set_title('Parameter Correlation')
                plt.colorbar(im, ax=axes[0, 1])
            
            # Top 10 results
            top_10 = self.results.all_results.head(10)
            axes[1, 0].bar(range(len(top_10)), top_10['score'])
            axes[1, 0].set_title('Top 10 Results')
            axes[1, 0].set_xlabel('Rank')
            axes[1, 0].set_ylabel(self.optimization_metric)
            
            # Parameter importance (if available)
            if len(param_cols) > 0:
                param_importance = {}
                for param in param_cols:
                    if self.results.all_results[param].dtype in ['float64', 'int64']:
                        corr = abs(self.results.all_results[param].corr(self.results.all_results['score']))
                        param_importance[param] = corr
                
                if param_importance:
                    params = list(param_importance.keys())
                    importance = list(param_importance.values())
                    axes[1, 1].bar(params, importance)
                    axes[1, 1].set_title('Parameter Importance')
                    axes[1, 1].set_ylabel('Correlation with Score')
                    axes[1, 1].tick_params(axis='x', rotation=45)
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"📊 Optimization results plot saved to: {save_path}")
            else:
                plt.show()
                
        except ImportError:
            print("Matplotlib not available for plotting")
    
    def export_results(self, filepath: str) -> None:
        """Export optimization results to Excel"""
        if not self.results:
            print("No results to export")
            return
        
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            # All results
            self.results.all_results.to_excel(writer, sheet_name='All Results', index=False)
            
            # Best parameters
            best_params_df = pd.DataFrame(list(self.results.best_params.items()), 
                                        columns=['Parameter', 'Value'])
            best_params_df.to_excel(writer, sheet_name='Best Parameters', index=False)
            
            # Summary
            summary_df = pd.DataFrame({
                'Metric': ['Best Score', 'Optimization Time', 'Method', 'Total Combinations'],
                'Value': [self.results.best_score, self.results.optimization_time, 
                         self.results.method, len(self.results.all_results)]
            })
            summary_df.to_excel(writer, sheet_name='Summary', index=False)
        
        print(f"📊 Optimization results exported to: {filepath}")

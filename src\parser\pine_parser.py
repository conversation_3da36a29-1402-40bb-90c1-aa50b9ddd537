"""
Main Pine Script Parser

Parses Pine Script source code and builds an Abstract Syntax Tree (AST)
that can be used for code generation and analysis.
"""

import re
from typing import List, Dict, Any, Optional, Tuple
from .lexer import PineScriptLexer, Token, TokenType
from .ast_nodes import *


class PineScriptParser:
    """Main parser for Pine Script source code"""
    
    def __init__(self):
        self.lexer: Optional[PineScriptLexer] = None
        self.tokens: List[Token] = []
        self.current_token_index = 0
        self.current_token: Optional[Token] = None
        
    def parse(self, source_code: str) -> PineScriptAST:
        """Parse Pine Script source code and return AST"""
        self.lexer = PineScriptLexer(source_code)
        self.tokens = self.lexer.tokenize()
        self.current_token_index = 0
        self.current_token = self.tokens[0] if self.tokens else None
        
        # Parse main components
        version = self._parse_version()
        indicator = self._parse_indicator_declaration()
        inputs = self._parse_inputs()
        constants = self._parse_constants()
        variables = self._parse_variables()
        functions = self._parse_functions()
        calculations = self._parse_calculations()
        plots = self._parse_plots()
        alerts = self._parse_alerts()
        
        return PineScriptAST(
            version=version,
            indicator=indicator,
            inputs=inputs,
            constants=constants,
            variables=variables,
            functions=functions,
            calculations=calculations,
            plots=plots,
            alerts=alerts,
            raw_code=source_code
        )
    
    def _advance(self) -> None:
        """Move to the next token"""
        if self.current_token_index < len(self.tokens) - 1:
            self.current_token_index += 1
            self.current_token = self.tokens[self.current_token_index]
    
    def _peek(self, offset: int = 1) -> Optional[Token]:
        """Look ahead at future tokens"""
        index = self.current_token_index + offset
        if index < len(self.tokens):
            return self.tokens[index]
        return None
    
    def _match(self, token_type: TokenType) -> bool:
        """Check if current token matches expected type"""
        return self.current_token and self.current_token.type == token_type
    
    def _consume(self, token_type: TokenType) -> Token:
        """Consume a token of expected type"""
        if not self._match(token_type):
            raise SyntaxError(f"Expected {token_type}, got {self.current_token.type if self.current_token else 'EOF'}")
        token = self.current_token
        self._advance()
        return token
    
    def _parse_version(self) -> PineScriptVersion:
        """Parse //@version=X directive"""
        for token in self.tokens:
            if token.type == TokenType.VERSION:
                version_match = re.search(r'version=(\d+)', token.value)
                if version_match:
                    version_num = version_match.group(1)
                    if version_num == "4":
                        return PineScriptVersion.V4
                    elif version_num == "5":
                        return PineScriptVersion.V5
                    elif version_num == "6":
                        return PineScriptVersion.V6
        
        # Default to V5 if not found
        return PineScriptVersion.V5
    
    def _parse_indicator_declaration(self) -> IndicatorDeclaration:
        """Parse indicator() declaration"""
        # Find indicator declaration in tokens
        for i, token in enumerate(self.tokens):
            if token.type == TokenType.KEYWORD and token.value == 'indicator':
                # Extract parameters from the indicator call
                # This is a simplified version - would need more robust parsing
                title = "Untitled Indicator"
                overlay = False
                
                # Look for string literal after 'indicator('
                j = i + 1
                while j < len(self.tokens) and self.tokens[j].type != TokenType.RPAREN:
                    if self.tokens[j].type == TokenType.STRING:
                        title = self.tokens[j].value.strip('"\'')
                        break
                    j += 1
                
                return IndicatorDeclaration(title=title, overlay=overlay)
        
        return IndicatorDeclaration(title="Unknown Indicator")
    
    def _parse_inputs(self) -> List[InputParameter]:
        """Parse input parameter declarations"""
        inputs = []

        # Convert tokens to text for regex parsing (more reliable for complex expressions)
        source_lines = self.lexer.source_code.split('\n') if self.lexer else []

        for line in source_lines:
            line = line.strip()

            # Match input declarations with various patterns
            input_patterns = [
                r'(\w+)\s*=\s*input\.bool\s*\(\s*([^,)]+)(?:,\s*[\'"]([^\'"]*)[\'"]\s*)?(?:,\s*group\s*=\s*([^,)]+))?',
                r'(\w+)\s*=\s*input\.int\s*\(\s*([^,)]+)(?:,\s*[\'"]([^\'"]*)[\'"]\s*)?(?:,\s*group\s*=\s*([^,)]+))?',
                r'(\w+)\s*=\s*input\.float\s*\(\s*([^,)]+)(?:,\s*[\'"]([^\'"]*)[\'"]\s*)?(?:,\s*group\s*=\s*([^,)]+))?',
                r'(\w+)\s*=\s*input\.string\s*\(\s*[\'"]([^\'"]*)[\'"]\s*,\s*[\'"]([^\'"]*)[\'"]\s*(?:,\s*group\s*=\s*([^,)]+))?',
                r'(\w+)\s*=\s*input\s*\(\s*([^,)]+)(?:,\s*[\'"]([^\'"]*)[\'"]\s*)?(?:,\s*group\s*=\s*([^,)]+))?'
            ]

            for pattern in input_patterns:
                match = re.search(pattern, line)
                if match:
                    var_name = match.group(1)
                    default_value = match.group(2)
                    title = match.group(3) if len(match.groups()) >= 3 and match.group(3) else var_name
                    group = match.group(4) if len(match.groups()) >= 4 and match.group(4) else ""

                    # Determine input type from pattern
                    input_type = InputType.STRING
                    if 'input.bool' in line:
                        input_type = InputType.BOOL
                        default_value = default_value.lower() == 'true'
                    elif 'input.int' in line:
                        input_type = InputType.INT
                        try:
                            default_value = int(default_value)
                        except:
                            default_value = 0
                    elif 'input.float' in line:
                        input_type = InputType.FLOAT
                        try:
                            default_value = float(default_value)
                        except:
                            default_value = 0.0
                    elif 'input.string' in line:
                        input_type = InputType.STRING
                        default_value = default_value.strip('\'"')

                    # Clean up group reference
                    if group.startswith('g_'):
                        group = group[2:]  # Remove g_ prefix
                    group = group.strip('\'"')

                    input_param = InputParameter(
                        name=var_name,
                        input_type=input_type,
                        default_value=default_value,
                        title=title,
                        group=group
                    )
                    inputs.append(input_param)
                    break

        return inputs
    
    def _parse_input_call(self, var_name: str, call_info: Dict) -> Optional[InputParameter]:
        """Parse an input.* function call"""
        func_name = call_info.get('name', '')
        args = call_info.get('args', [])
        kwargs = call_info.get('kwargs', {})
        
        # Determine input type from function name
        input_type_map = {
            'input.bool': InputType.BOOL,
            'input.int': InputType.INT,
            'input.float': InputType.FLOAT,
            'input.string': InputType.STRING,
            'input.color': InputType.COLOR,
            'input': InputType.STRING  # Default
        }
        
        input_type = input_type_map.get(func_name, InputType.STRING)
        default_value = args[0] if args else None
        title = args[1] if len(args) > 1 else var_name
        
        return InputParameter(
            name=var_name,
            input_type=input_type,
            default_value=default_value,
            title=title,
            tooltip=kwargs.get('tooltip', ''),
            group=kwargs.get('group', ''),
            minval=kwargs.get('minval'),
            maxval=kwargs.get('maxval'),
            step=kwargs.get('step'),
            options=kwargs.get('options')
        )
    
    def _extract_function_call(self, start_index: int) -> Optional[Dict]:
        """Extract function call information starting from given index"""
        if start_index >= len(self.tokens):
            return None
            
        func_token = self.tokens[start_index]
        if func_token.type != TokenType.IDENTIFIER:
            return None
        
        # This is a simplified extraction - would need more robust parsing
        # for complex function calls with nested expressions
        return {
            'name': func_token.value,
            'args': [],
            'kwargs': {}
        }
    
    def _parse_constants(self) -> List[Variable]:
        """Parse constant declarations (const keyword)"""
        constants = []
        source_lines = self.lexer.source_code.split('\n') if self.lexer else []

        for line in source_lines:
            line = line.strip()
            # Match const declarations
            const_match = re.match(r'const\s+(\w+)\s+(\w+)\s*=\s*(.+)', line)
            if const_match:
                var_type = const_match.group(1)
                var_name = const_match.group(2)
                var_value = const_match.group(3)

                # Parse value based on type
                if var_type == 'int':
                    try:
                        var_value = int(var_value)
                    except:
                        var_value = 0
                elif var_type == 'float':
                    try:
                        var_value = float(var_value)
                    except:
                        var_value = 0.0
                elif var_type == 'string':
                    var_value = var_value.strip('\'"')

                constants.append(Variable(
                    name=var_name,
                    value=var_value,
                    var_type=var_type,
                    is_const=True
                ))

        return constants

    def _parse_variables(self) -> List[Variable]:
        """Parse variable declarations (var keyword and assignments)"""
        variables = []
        source_lines = self.lexer.source_code.split('\n') if self.lexer else []

        for line in source_lines:
            line = line.strip()
            # Match var declarations
            var_match = re.match(r'var\s+(\w+(?:\[\])?\s+)?(\w+)\s*=\s*(.+)', line)
            if var_match:
                var_type = var_match.group(1).strip() if var_match.group(1) else None
                var_name = var_match.group(2)
                var_value = var_match.group(3)

                is_series = '[]' in (var_type or '')

                variables.append(Variable(
                    name=var_name,
                    value=var_value,
                    var_type=var_type,
                    is_series=is_series
                ))

        return variables

    def _parse_functions(self) -> List[FunctionDefinition]:
        """Parse user-defined functions"""
        functions = []
        source_lines = self.lexer.source_code.split('\n') if self.lexer else []

        i = 0
        while i < len(source_lines):
            line = source_lines[i].strip()

            # Match function definitions
            func_match = re.match(r'(\w+)\s*\(([^)]*)\)\s*=>', line)
            if func_match:
                func_name = func_match.group(1)
                params_str = func_match.group(2)

                # Parse parameters
                params = []
                if params_str.strip():
                    params = [p.strip() for p in params_str.split(',')]

                # Collect function body (simplified - would need proper parsing)
                body = []
                i += 1
                indent_level = 0
                while i < len(source_lines):
                    body_line = source_lines[i]
                    if body_line.strip() and not body_line.startswith('    '):
                        break
                    body.append(body_line)
                    i += 1

                functions.append(FunctionDefinition(
                    name=func_name,
                    parameters=params,
                    body=body
                ))
                continue

            i += 1

        return functions

    def _parse_calculations(self) -> List[Any]:
        """Parse calculation statements and expressions"""
        calculations = []
        # This would contain the main indicator logic
        # For now, we'll extract key calculation patterns
        source_lines = self.lexer.source_code.split('\n') if self.lexer else []

        for line in source_lines:
            line = line.strip()

            # Skip comments and empty lines
            if not line or line.startswith('//'):
                continue

            # Look for key calculation patterns
            if any(keyword in line for keyword in ['ta.', 'math.', '=', 'if', 'for']):
                if not any(skip in line for skip in ['input.', 'const ', 'var ', '//@']):
                    calculations.append(line)

        return calculations

    def _parse_plots(self) -> List[PlotStatement]:
        """Parse plot() statements"""
        plots = []
        source_lines = self.lexer.source_code.split('\n') if self.lexer else []

        for line in source_lines:
            line = line.strip()

            # Match plot statements
            plot_match = re.search(r'plot\s*\(\s*([^,)]+)(?:,\s*title\s*=\s*[\'"]([^\'"]*)[\'"]\s*)?(?:,\s*color\s*=\s*([^,)]+))?', line)
            if plot_match:
                series = plot_match.group(1)
                title = plot_match.group(2) if plot_match.group(2) else series
                color = plot_match.group(3) if plot_match.group(3) else ""

                plots.append(PlotStatement(
                    series=series,
                    title=title,
                    color=color
                ))

        return plots

    def _parse_alerts(self) -> List[AlertCondition]:
        """Parse alert conditions"""
        alerts = []
        source_lines = self.lexer.source_code.split('\n') if self.lexer else []

        for line in source_lines:
            line = line.strip()

            # Match alert conditions
            alert_match = re.search(r'alertcondition\s*\(\s*([^,)]+)(?:,\s*title\s*=\s*[\'"]([^\'"]*)[\'"]\s*)?(?:,\s*message\s*=\s*[\'"]([^\'"]*)[\'"]\s*)?', line)
            if alert_match:
                condition = alert_match.group(1)
                title = alert_match.group(2) if alert_match.group(2) else ""
                message = alert_match.group(3) if alert_match.group(3) else ""

                alerts.append(AlertCondition(
                    condition=condition,
                    title=title,
                    message=message
                ))

        return alerts

"""
Backtesting Engine Module

Provides comprehensive backtesting functionality for generated strategies
including realistic trading costs, performance metrics, and analysis tools.
"""

from .backtest_engine import BacktestEngine
from .performance_analyzer import PerformanceAnalyzer
from .data_handler import DataHandler
from .portfolio_manager import PortfolioManager

__all__ = [
    "BacktestEngine",
    "PerformanceAnalyzer",
    "DataHandler",
    "PortfolioManager"
]

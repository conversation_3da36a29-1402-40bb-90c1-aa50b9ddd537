# Pine Script to Python Converter
# Main package initialization

__version__ = "1.0.0"
__author__ = "Pine Script Converter Team"
__description__ = "Convert TradingView Pine Script indicators to Python with backtesting capabilities"

from .parser import PineScriptParser
from .generator import PythonCodeGenerator
from .strategy import StrategyGenerator
from .backtesting import BacktestEngine
from .optimization import ParameterOptimizer

__all__ = [
    "PineScriptParser",
    "PythonCodeGenerator", 
    "StrategyGenerator",
    "BacktestEngine",
    "ParameterOptimizer"
]

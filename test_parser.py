#!/usr/bin/env python3
"""
Test script for Pine Script Parser

Tests the parser with the Brandon algo Pine Script file.
"""

import sys
import os

# Add src to path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

# Import modules
try:
    from parser.pine_parser import PineScriptParser
    from generator.python_generator import PythonCodeGenerator
except ImportError as e:
    print(f"Import error: {e}")
    print("Trying alternative import method...")

    # Alternative import method
    import importlib.util

    # Load parser module
    parser_spec = importlib.util.spec_from_file_location(
        "pine_parser",
        os.path.join(src_path, "parser", "pine_parser.py")
    )
    parser_module = importlib.util.module_from_spec(parser_spec)
    parser_spec.loader.exec_module(parser_module)
    PineScriptParser = parser_module.PineScriptParser

    # Load generator module
    generator_spec = importlib.util.spec_from_file_location(
        "python_generator",
        os.path.join(src_path, "generator", "python_generator.py")
    )
    generator_module = importlib.util.module_from_spec(generator_spec)

    # We need to handle the relative imports manually
    print("Setting up modules for relative imports...")

    # For now, let's create a simpler test


def test_brandon_algo_parsing():
    """Test parsing the Brandon algo Pine Script"""
    
    # Read the Pine Script file
    try:
        with open('brandon algo.pine', 'r', encoding='utf-8') as f:
            pine_script_code = f.read()
    except FileNotFoundError:
        print("Error: 'brandon algo.pine' file not found!")
        return False
    
    print("🔍 Testing Pine Script Parser...")
    print(f"📄 File size: {len(pine_script_code)} characters")
    print(f"📄 Lines: {len(pine_script_code.split(chr(10)))}")
    
    # Initialize parser
    parser = PineScriptParser()
    
    try:
        # Parse the Pine Script
        print("\n⚙️  Parsing Pine Script...")
        ast = parser.parse(pine_script_code)
        
        # Display parsing results
        print(f"✅ Parsing completed successfully!")
        print(f"📊 Results:")
        print(f"   - Version: {ast.version.value}")
        print(f"   - Indicator: {ast.indicator.title}")
        print(f"   - Inputs: {len(ast.inputs)}")
        print(f"   - Constants: {len(ast.constants)}")
        print(f"   - Variables: {len(ast.variables)}")
        print(f"   - Functions: {len(ast.functions)}")
        print(f"   - Calculations: {len(ast.calculations)}")
        print(f"   - Plots: {len(ast.plots)}")
        print(f"   - Alerts: {len(ast.alerts)}")
        
        # Show some input parameters
        print(f"\n📝 Sample Input Parameters:")
        for i, input_param in enumerate(ast.inputs[:10]):  # Show first 10
            print(f"   {i+1}. {input_param.name} ({input_param.input_type.value}) = {input_param.default_value}")
            if input_param.group:
                print(f"      Group: {input_param.group}")
        
        if len(ast.inputs) > 10:
            print(f"   ... and {len(ast.inputs) - 10} more")
        
        # Show some constants
        if ast.constants:
            print(f"\n🔢 Constants:")
            for const in ast.constants[:5]:
                print(f"   - {const.name} = {const.value}")
        
        # Show some functions
        if ast.functions:
            print(f"\n🔧 User Functions:")
            for func in ast.functions[:5]:
                print(f"   - {func.name}({', '.join(func.parameters)})")
        
        return ast
        
    except Exception as e:
        print(f"❌ Parsing failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


def test_python_generation(ast):
    """Test Python code generation"""
    if not ast:
        print("❌ Cannot test code generation - parsing failed")
        return False
    
    print("\n🐍 Testing Python Code Generation...")
    
    try:
        # Initialize generator
        generator = PythonCodeGenerator()
        
        # Generate Python code
        python_code = generator.generate(ast)
        
        print(f"✅ Code generation completed!")
        print(f"📄 Generated code length: {len(python_code)} characters")
        
        # Save generated code
        output_file = "generated_brandon_algo.py"
        generator.save_to_file(output_file)
        print(f"💾 Saved to: {output_file}")
        
        # Show first few lines
        lines = python_code.split('\n')
        print(f"\n📝 First 20 lines of generated code:")
        for i, line in enumerate(lines[:20]):
            print(f"   {i+1:2d}: {line}")
        
        if len(lines) > 20:
            print(f"   ... and {len(lines) - 20} more lines")
        
        return True
        
    except Exception as e:
        print(f"❌ Code generation failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    print("🚀 Pine Script to Python Converter - Test Suite")
    print("=" * 60)
    
    # Test parsing
    ast = test_brandon_algo_parsing()
    
    # Test code generation
    if ast:
        test_python_generation(ast)
    
    print("\n" + "=" * 60)
    print("🏁 Test completed!")


if __name__ == "__main__":
    main()

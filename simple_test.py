#!/usr/bin/env python3
"""
Simple test for Pine Script parsing functionality
"""

import re
from typing import List, Dict, Any
from dataclasses import dataclass
from enum import Enum


class InputType(Enum):
    BOOL = "bool"
    INT = "int"
    FLOAT = "float"
    STRING = "string"


@dataclass
class InputParameter:
    name: str
    input_type: InputType
    default_value: Any
    title: str = ""
    group: str = ""


def parse_pine_script_inputs(source_code: str) -> List[InputParameter]:
    """Simple Pine Script input parser"""
    inputs = []
    lines = source_code.split('\n')
    
    for line in lines:
        line = line.strip()
        
        # Skip comments and empty lines
        if not line or line.startswith('//'):
            continue
        
        # Match different input patterns
        patterns = [
            (r'(\w+)\s*=\s*input\.bool\s*\(\s*([^,)]+)(?:,\s*[\'"]([^\'"]*)[\'"]\s*)?(?:.*group\s*=\s*([^,)]+))?', InputType.BOOL),
            (r'(\w+)\s*=\s*input\.int\s*\(\s*([^,)]+)(?:,\s*[\'"]([^\'"]*)[\'"]\s*)?(?:.*group\s*=\s*([^,)]+))?', InputType.INT),
            (r'(\w+)\s*=\s*input\.float\s*\(\s*([^,)]+)(?:,\s*[\'"]([^\'"]*)[\'"]\s*)?(?:.*group\s*=\s*([^,)]+))?', InputType.FLOAT),
            (r'(\w+)\s*=\s*input\.string\s*\(\s*[\'"]([^\'"]*)[\'"]\s*,\s*[\'"]([^\'"]*)[\'"]\s*(?:.*group\s*=\s*([^,)]+))?', InputType.STRING),
        ]
        
        for pattern, input_type in patterns:
            match = re.search(pattern, line)
            if match:
                var_name = match.group(1)
                
                if input_type == InputType.STRING:
                    default_value = match.group(2)
                    title = match.group(3) if len(match.groups()) >= 3 else var_name
                    group = match.group(4) if len(match.groups()) >= 4 and match.group(4) else ""
                else:
                    default_value = match.group(2)
                    title = match.group(3) if len(match.groups()) >= 3 and match.group(3) else var_name
                    group = match.group(4) if len(match.groups()) >= 4 and match.group(4) else ""
                
                # Convert default value
                if input_type == InputType.BOOL:
                    default_value = default_value.lower() == 'true'
                elif input_type == InputType.INT:
                    try:
                        default_value = int(default_value)
                    except:
                        default_value = 0
                elif input_type == InputType.FLOAT:
                    try:
                        default_value = float(default_value)
                    except:
                        default_value = 0.0
                elif input_type == InputType.STRING:
                    default_value = default_value.strip('\'"')
                
                # Clean group
                if group:
                    group = group.strip('\'"').replace('g_', '')
                
                inputs.append(InputParameter(
                    name=var_name,
                    input_type=input_type,
                    default_value=default_value,
                    title=title or var_name,
                    group=group
                ))
                break
    
    return inputs


def analyze_pine_script(filename: str):
    """Analyze Pine Script file"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"❌ File '{filename}' not found!")
        return
    
    print(f"🔍 Analyzing Pine Script: {filename}")
    print(f"📄 File size: {len(content)} characters")
    print(f"📄 Lines: {len(content.split(chr(10)))}")
    
    # Extract version
    version_match = re.search(r'//@version=(\d+)', content)
    version = version_match.group(1) if version_match else "Unknown"
    
    # Extract indicator title
    indicator_match = re.search(r'indicator\s*\(\s*[\'"]([^\'"]*)[\'"]\s*', content)
    title = indicator_match.group(1) if indicator_match else "Unknown Indicator"
    
    print(f"📊 Version: {version}")
    print(f"📊 Title: {title}")
    
    # Parse inputs
    inputs = parse_pine_script_inputs(content)
    print(f"📊 Input parameters found: {len(inputs)}")
    
    # Group inputs by category
    groups = {}
    for inp in inputs:
        group = inp.group or "General"
        if group not in groups:
            groups[group] = []
        groups[group].append(inp)
    
    print(f"\n📝 Input Parameters by Group:")
    for group_name, group_inputs in groups.items():
        print(f"\n   🏷️  {group_name} ({len(group_inputs)} parameters):")
        for inp in group_inputs[:5]:  # Show first 5 in each group
            print(f"      • {inp.name} ({inp.input_type.value}) = {inp.default_value}")
            if inp.title != inp.name:
                print(f"        Title: {inp.title}")
        if len(group_inputs) > 5:
            print(f"      ... and {len(group_inputs) - 5} more")
    
    # Count function usage
    functions = [
        'ta.sma', 'ta.ema', 'ta.rsi', 'ta.macd', 'ta.dmi', 'ta.atr',
        'math.abs', 'math.max', 'math.min', 'math.pow', 'math.sqrt',
        'array.new_float', 'array.push', 'array.get', 'array.set'
    ]
    
    function_counts = {}
    for func in functions:
        count = len(re.findall(re.escape(func), content))
        if count > 0:
            function_counts[func] = count
    
    if function_counts:
        print(f"\n🔧 Function Usage:")
        for func, count in sorted(function_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"   • {func}: {count} times")
    
    # Look for key patterns
    patterns = {
        'Variables (var)': r'\bvar\s+\w+',
        'Constants (const)': r'\bconst\s+\w+',
        'Functions': r'\w+\s*\([^)]*\)\s*=>',
        'Plots': r'\bplot\s*\(',
        'Alerts': r'\balertcondition\s*\(',
        'If statements': r'\bif\s+',
        'For loops': r'\bfor\s+',
        'Arrays': r'\barray\.',
        'Request calls': r'\brequest\.',
    }
    
    print(f"\n📈 Code Patterns:")
    for pattern_name, pattern in patterns.items():
        count = len(re.findall(pattern, content))
        if count > 0:
            print(f"   • {pattern_name}: {count}")
    
    return {
        'version': version,
        'title': title,
        'inputs': inputs,
        'function_counts': function_counts,
        'content': content
    }


def main():
    """Main function"""
    print("🚀 Pine Script Analyzer")
    print("=" * 50)
    
    result = analyze_pine_script('brandon algo.pine')
    
    if result:
        print(f"\n✅ Analysis completed successfully!")
        print(f"📊 Summary:")
        print(f"   - Version: Pine Script v{result['version']}")
        print(f"   - Title: {result['title']}")
        print(f"   - Input Parameters: {len(result['inputs'])}")
        print(f"   - Functions Used: {len(result['function_counts'])}")
        
        # Generate a simple Python template
        print(f"\n🐍 Generating Python template...")
        
        python_template = f'''"""
{result['title']}
Converted from Pine Script v{result['version']}

This is a template conversion - full implementation would require
more sophisticated parsing and conversion logic.
"""

import pandas as pd
import numpy as np
import talib as ta


class {result['title'].replace(' ', '').replace('[', '').replace(']', '')}:
    """Pine Script Indicator converted to Python"""
    
    def __init__(self'''
        
        # Add parameters
        for inp in result['inputs'][:10]:  # First 10 parameters
            if inp.input_type == InputType.STRING:
                python_template += f', {inp.name}="{inp.default_value}"'
            else:
                python_template += f', {inp.name}={inp.default_value}'
        
        python_template += '''):
        """Initialize indicator parameters"""'''
        
        for inp in result['inputs'][:10]:
            python_template += f'''
        self.{inp.name} = {inp.name}'''
        
        python_template += '''
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate indicator values"""
        # Extract OHLCV data
        close = data['close'].values
        high = data['high'].values
        low = data['low'].values
        volume = data['volume'].values
        
        # Initialize result arrays
        result_df = data.copy()
        
        # TODO: Implement indicator calculations
        # This would include the converted Pine Script logic
        
        return result_df
'''
        
        # Save template
        with open('brandon_algo_template.py', 'w', encoding='utf-8') as f:
            f.write(python_template)
        
        print(f"💾 Python template saved to: brandon_algo_template.py")
    
    print("\n" + "=" * 50)
    print("🏁 Analysis completed!")


if __name__ == "__main__":
    main()

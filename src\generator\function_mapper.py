"""
<PERSON> Script to Python Function Mapper

Maps Pine Script built-in functions to their Python equivalents
using pandas, numpy, TA-Lib, and pandas_ta.
"""

from typing import Dict, Callable, Any, List, Optional
import pandas as pd
import numpy as np


class PineFunctionMapper:
    """Maps Pine Script functions to Python implementations"""
    
    def __init__(self):
        self.function_map = self._build_function_map()
        self.ta_lib_functions = self._build_talib_map()
        self.pandas_ta_functions = self._build_pandas_ta_map()
    
    def _build_function_map(self) -> Dict[str, Dict[str, Any]]:
        """Build the main function mapping dictionary"""
        return {
            # Math functions
            'math.abs': {
                'python': 'np.abs',
                'imports': ['numpy as np'],
                'description': 'Absolute value'
            },
            'math.max': {
                'python': 'np.maximum',
                'imports': ['numpy as np'],
                'description': 'Maximum of two values'
            },
            'math.min': {
                'python': 'np.minimum', 
                'imports': ['numpy as np'],
                'description': 'Minimum of two values'
            },
            'math.pow': {
                'python': 'np.power',
                'imports': ['numpy as np'],
                'description': 'Power function'
            },
            'math.sqrt': {
                'python': 'np.sqrt',
                'imports': ['numpy as np'],
                'description': 'Square root'
            },
            'math.exp': {
                'python': 'np.exp',
                'imports': ['numpy as np'],
                'description': 'Exponential function'
            },
            'math.log': {
                'python': 'np.log',
                'imports': ['numpy as np'],
                'description': 'Natural logarithm'
            },
            'math.sin': {
                'python': 'np.sin',
                'imports': ['numpy as np'],
                'description': 'Sine function'
            },
            'math.cos': {
                'python': 'np.cos',
                'imports': ['numpy as np'],
                'description': 'Cosine function'
            },
            'math.round': {
                'python': 'np.round',
                'imports': ['numpy as np'],
                'description': 'Round to nearest integer'
            },
            'math.sign': {
                'python': 'np.sign',
                'imports': ['numpy as np'],
                'description': 'Sign function'
            },
            'math.random': {
                'python': 'np.random.random',
                'imports': ['numpy as np'],
                'description': 'Random number generator'
            },
            
            # Technical Analysis functions
            'ta.sma': {
                'python': 'ta.SMA',
                'imports': ['talib as ta'],
                'description': 'Simple Moving Average',
                'params': ['close', 'length']
            },
            'ta.ema': {
                'python': 'ta.EMA',
                'imports': ['talib as ta'],
                'description': 'Exponential Moving Average',
                'params': ['close', 'length']
            },
            'ta.wma': {
                'python': 'ta.WMA',
                'imports': ['talib as ta'],
                'description': 'Weighted Moving Average',
                'params': ['close', 'length']
            },
            'ta.rma': {
                'python': 'self._rma',
                'imports': [],
                'description': 'Running Moving Average (custom implementation)',
                'custom': True
            },
            'ta.vwma': {
                'python': 'self._vwma',
                'imports': [],
                'description': 'Volume Weighted Moving Average (custom implementation)',
                'custom': True
            },
            'ta.rsi': {
                'python': 'ta.RSI',
                'imports': ['talib as ta'],
                'description': 'Relative Strength Index',
                'params': ['close', 'length']
            },
            'ta.macd': {
                'python': 'ta.MACD',
                'imports': ['talib as ta'],
                'description': 'MACD',
                'params': ['close', 'fast', 'slow', 'signal'],
                'returns_tuple': True
            },
            'ta.dmi': {
                'python': 'self._dmi',
                'imports': [],
                'description': 'Directional Movement Index (custom implementation)',
                'custom': True
            },
            'ta.atr': {
                'python': 'ta.ATR',
                'imports': ['talib as ta'],
                'description': 'Average True Range',
                'params': ['high', 'low', 'close', 'length']
            },
            'ta.stoch': {
                'python': 'ta.STOCH',
                'imports': ['talib as ta'],
                'description': 'Stochastic Oscillator',
                'returns_tuple': True
            },
            'ta.obv': {
                'python': 'ta.OBV',
                'imports': ['talib as ta'],
                'description': 'On Balance Volume',
                'params': ['close', 'volume']
            },
            'ta.change': {
                'python': 'self._change',
                'imports': [],
                'description': 'Price change over n periods (custom implementation)',
                'custom': True
            },
            'ta.stdev': {
                'python': 'self._stdev',
                'imports': [],
                'description': 'Standard deviation (custom implementation)',
                'custom': True
            },
            'ta.rising': {
                'python': 'self._rising',
                'imports': [],
                'description': 'Check if series is rising (custom implementation)',
                'custom': True
            },
            'ta.falling': {
                'python': 'self._falling',
                'imports': [],
                'description': 'Check if series is falling (custom implementation)',
                'custom': True
            },
            'ta.pivothigh': {
                'python': 'self._pivothigh',
                'imports': [],
                'description': 'Pivot high detection (custom implementation)',
                'custom': True
            },
            'ta.pivotlow': {
                'python': 'self._pivotlow',
                'imports': [],
                'description': 'Pivot low detection (custom implementation)',
                'custom': True
            },
            
            # Array functions
            'array.new_float': {
                'python': 'np.zeros',
                'imports': ['numpy as np'],
                'description': 'Create new float array'
            },
            'array.push': {
                'python': 'np.append',
                'imports': ['numpy as np'],
                'description': 'Push value to array'
            },
            'array.get': {
                'python': 'lambda arr, idx: arr[idx]',
                'imports': [],
                'description': 'Get array element'
            },
            'array.set': {
                'python': 'lambda arr, idx, val: arr.__setitem__(idx, val)',
                'imports': [],
                'description': 'Set array element'
            },
            'array.size': {
                'python': 'len',
                'imports': [],
                'description': 'Get array size'
            },
            'array.sum': {
                'python': 'np.sum',
                'imports': ['numpy as np'],
                'description': 'Sum array elements'
            },
            
            # Bar state functions
            'barstate.isfirst': {
                'python': 'self._is_first_bar',
                'imports': [],
                'description': 'Check if first bar (custom implementation)',
                'custom': True
            },
            'barstate.islast': {
                'python': 'self._is_last_bar',
                'imports': [],
                'description': 'Check if last bar (custom implementation)',
                'custom': True
            },
            
            # Request functions (multi-timeframe)
            'request.security': {
                'python': 'self._request_security',
                'imports': [],
                'description': 'Request data from different timeframe (custom implementation)',
                'custom': True
            }
        }
    
    def _build_talib_map(self) -> Dict[str, str]:
        """Build TA-Lib specific function mappings"""
        return {
            'SMA': 'ta.SMA(close, timeperiod=length)',
            'EMA': 'ta.EMA(close, timeperiod=length)',
            'WMA': 'ta.WMA(close, timeperiod=length)',
            'RSI': 'ta.RSI(close, timeperiod=length)',
            'MACD': 'ta.MACD(close, fastperiod=fast, slowperiod=slow, signalperiod=signal)',
            'ATR': 'ta.ATR(high, low, close, timeperiod=length)',
            'STOCH': 'ta.STOCH(high, low, close)',
            'OBV': 'ta.OBV(close, volume)'
        }
    
    def _build_pandas_ta_map(self) -> Dict[str, str]:
        """Build pandas_ta specific function mappings"""
        return {
            'sma': 'ta.sma(close, length=length)',
            'ema': 'ta.ema(close, length=length)',
            'rsi': 'ta.rsi(close, length=length)',
            'macd': 'ta.macd(close, fast=fast, slow=slow, signal=signal)',
            'atr': 'ta.atr(high, low, close, length=length)',
            'obv': 'ta.obv(close, volume)'
        }
    
    def get_python_equivalent(self, pine_function: str) -> Optional[Dict[str, Any]]:
        """Get Python equivalent for a Pine Script function"""
        return self.function_map.get(pine_function)
    
    def get_required_imports(self, pine_functions: List[str]) -> List[str]:
        """Get all required imports for a list of Pine Script functions"""
        imports = set()
        
        for func in pine_functions:
            mapping = self.get_python_equivalent(func)
            if mapping and 'imports' in mapping:
                imports.update(mapping['imports'])
        
        return sorted(list(imports))
    
    def is_custom_function(self, pine_function: str) -> bool:
        """Check if function requires custom implementation"""
        mapping = self.get_python_equivalent(pine_function)
        return mapping and mapping.get('custom', False)
    
    def get_custom_functions(self, pine_functions: List[str]) -> List[str]:
        """Get list of functions that need custom implementations"""
        custom_funcs = []
        
        for func in pine_functions:
            if self.is_custom_function(func):
                custom_funcs.append(func)
        
        return custom_funcs

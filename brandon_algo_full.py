"""
<PERSON> Beta V4.8 - Python Implementation

Converted from Pine Script v6 indicator with full functionality including:
- Multi-indicator trend analysis (MACD, RSI, DMI)
- Dynamic weight optimization
- Machine learning-inspired adaptive algorithms
- Volume and swing point analysis
- Sentiment analysis for crypto markets
- Multi-timeframe support
"""

import pandas as pd
import numpy as np
import talib as ta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')


class BrandonAlgoIndicator:
    """
    Brandon <PERSON> Algo Beta V4.8 - Python Implementation
    
    A comprehensive trend analysis indicator that combines multiple technical
    indicators with adaptive weighting and machine learning concepts.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the indicator with configuration parameters"""
        # Set default configuration
        self.config = self._get_default_config()
        
        # Update with user configuration
        if config:
            self.config.update(config)
        
        # Initialize state variables
        self._current_index = 0
        self._data_length = 0
        self._ml_weights = np.array([1.0, 1.0, 1.0])  # [macd, rsi, dmi]
        self._ml_accuracy = 50.0
        self._ml_update_counter = 0
        
        # Performance tracking arrays
        self._macd_performance = []
        self._rsi_performance = []
        self._dmi_performance = []
        self._price_changes = []
    
    def _get_default_config(self) -> Dict:
        """Get default configuration parameters"""
        return {
            # Trend Detection
            'use_macd': True,
            'use_rsi': True,
            'use_dmi': True,
            'trend_period': 21,
            'adx_period': 18,
            'adx_threshold_trending': 22,
            'volatility_impact': 0.5,
            'vol_ma_period': 10,
            'volatility_lookback': 75,
            
            # MACD Settings
            'macd_fast_length': 12,
            'macd_slow_length': 26,
            'macd_signal_length': 9,
            
            # RSI Settings
            'rsi_length': 14,
            'rsi_period1': 14,
            'rsi_period2': 17,
            'stoch_length': 14,
            'smooth_k': 3,
            'smooth_d': 3,
            
            # DMI Settings
            'dmi_length': 14,
            'adx_threshold': 25,
            
            # Weights
            'use_dynamic_weights': True,
            'macd_weight': 1.0,
            'rsi_weight': 1.0,
            'dmi_weight': 1.0,
            'dynamic_weight_strength': 0.45,
            
            # ML Settings
            'use_ml': True,
            'ml_weight_influence': 0.7,
            'ml_lookback': 50,
            'ml_update_frequency': 8,
            'ml_learning_rate': 0.03,
            
            # Volume Analysis
            'use_volume_analysis': True,
            'volume_impact': 0.25,
            'obv_period': 20,
            'volume_divergence_length': 14,
            
            # Swing Points
            'use_swing_points': True,
            'swing_impact': 0.35,
            'swing_strength': 3,
            'swing_lookback': 50,
            
            # Sentiment Analysis
            'use_sentiment_analysis': True,
            'sentiment_weight': 0.25,
            'sentiment_lookback': 10,
            'sentiment_volatility_filter': True
        }
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate indicator values for the given OHLCV data
        
        Args:
            data: DataFrame with columns ['open', 'high', 'low', 'close', 'volume']
            
        Returns:
            DataFrame with calculated indicator values and trend scores
        """
        if data.empty:
            return pd.DataFrame()
        
        # Validate required columns
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Extract OHLCV data
        open_prices = data['open'].values
        high_prices = data['high'].values
        low_prices = data['low'].values
        close_prices = data['close'].values
        volume = data['volume'].values
        
        self._data_length = len(data)
        
        # Calculate base indicators
        macd_line, macd_signal, macd_histogram = self._calculate_macd(close_prices)
        rsi_values = self._calculate_rsi(close_prices)
        di_plus, di_minus, adx_values = self._calculate_dmi(high_prices, low_prices, close_prices)
        
        # Calculate volume analysis if enabled
        volume_score = np.zeros(self._data_length)
        if self.config['use_volume_analysis']:
            volume_score = self._calculate_volume_analysis(close_prices, volume)
        
        # Calculate swing point analysis if enabled
        swing_score = np.zeros(self._data_length)
        if self.config['use_swing_points']:
            swing_score = self._calculate_swing_analysis(high_prices, low_prices, close_prices)
        
        # Calculate sentiment analysis if enabled
        sentiment_score = np.zeros(self._data_length)
        if self.config['use_sentiment_analysis']:
            sentiment_score = self._calculate_sentiment_analysis(
                open_prices, high_prices, low_prices, close_prices, volume)
        
        # Calculate trend scores
        trend_scores = self._calculate_trend_scores(
            macd_histogram, rsi_values, di_plus, di_minus, adx_values,
            volume_score, swing_score, sentiment_score, close_prices)
        
        # Create result DataFrame
        result_df = data.copy()
        result_df['trend_score'] = trend_scores
        result_df['macd_line'] = macd_line
        result_df['macd_signal'] = macd_signal
        result_df['macd_histogram'] = macd_histogram
        result_df['rsi'] = rsi_values
        result_df['di_plus'] = di_plus
        result_df['di_minus'] = di_minus
        result_df['adx'] = adx_values
        result_df['volume_score'] = volume_score
        result_df['swing_score'] = swing_score
        result_df['sentiment_score'] = sentiment_score
        
        # Add trading signals
        result_df['bullish_signal'] = trend_scores > 70
        result_df['bearish_signal'] = trend_scores < 30
        result_df['strong_bullish'] = trend_scores > 80
        result_df['strong_bearish'] = trend_scores < 20
        
        return result_df

    def _calculate_macd(self, close: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Calculate MACD indicator"""
        if not self.config['use_macd']:
            return np.zeros(len(close)), np.zeros(len(close)), np.zeros(len(close))

        macd_line, macd_signal, macd_histogram = ta.MACD(
            close,
            fastperiod=self.config['macd_fast_length'],
            slowperiod=self.config['macd_slow_length'],
            signalperiod=self.config['macd_signal_length']
        )

        # Handle NaN values
        macd_line = np.nan_to_num(macd_line, nan=0.0)
        macd_signal = np.nan_to_num(macd_signal, nan=0.0)
        macd_histogram = np.nan_to_num(macd_histogram, nan=0.0)

        return macd_line, macd_signal, macd_histogram

    def _calculate_rsi(self, close: np.ndarray) -> np.ndarray:
        """Calculate RSI indicator"""
        if not self.config['use_rsi']:
            return np.full(len(close), 50.0)

        rsi = ta.RSI(close, timeperiod=self.config['rsi_length'])
        return np.nan_to_num(rsi, nan=50.0)

    def _calculate_dmi(self, high: np.ndarray, low: np.ndarray, close: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Calculate DMI (Directional Movement Index)"""
        if not self.config['use_dmi']:
            return np.zeros(len(close)), np.zeros(len(close)), np.full(len(close), 25.0)

        # Calculate True Range
        tr1 = high - low
        tr2 = np.abs(high - np.roll(close, 1))
        tr3 = np.abs(low - np.roll(close, 1))
        tr = np.maximum(tr1, np.maximum(tr2, tr3))

        # Calculate Directional Movement
        dm_plus = np.where((high - np.roll(high, 1)) > (np.roll(low, 1) - low),
                          np.maximum(high - np.roll(high, 1), 0), 0)
        dm_minus = np.where((np.roll(low, 1) - low) > (high - np.roll(high, 1)),
                           np.maximum(np.roll(low, 1) - low, 0), 0)

        # Smooth with RMA (Running Moving Average)
        length = self.config['dmi_length']
        tr_smooth = self._rma(tr, length)
        dm_plus_smooth = self._rma(dm_plus, length)
        dm_minus_smooth = self._rma(dm_minus, length)

        # Calculate DI+ and DI-
        di_plus = 100 * dm_plus_smooth / np.maximum(tr_smooth, 0.001)
        di_minus = 100 * dm_minus_smooth / np.maximum(tr_smooth, 0.001)

        # Calculate ADX
        dx = 100 * np.abs(di_plus - di_minus) / np.maximum(di_plus + di_minus, 0.001)
        adx = self._rma(dx, self.config['adx_period'])

        return di_plus, di_minus, adx

    def _rma(self, series: np.ndarray, length: int) -> np.ndarray:
        """Running Moving Average (Pine Script ta.rma equivalent)"""
        result = np.full_like(series, np.nan)
        if len(series) == 0 or length <= 0:
            return result

        alpha = 1.0 / length
        result[0] = series[0]

        for i in range(1, len(series)):
            if not np.isnan(series[i]):
                if np.isnan(result[i-1]):
                    result[i] = series[i]
                else:
                    result[i] = alpha * series[i] + (1 - alpha) * result[i-1]
            else:
                result[i] = result[i-1] if not np.isnan(result[i-1]) else 0.0

        return result

    def _calculate_volume_analysis(self, close: np.ndarray, volume: np.ndarray) -> np.ndarray:
        """Calculate volume-based trend score"""
        volume_score = np.zeros(len(close))

        # Calculate On-Balance Volume
        obv = ta.OBV(close, volume)
        smoothed_obv = ta.EMA(obv, timeperiod=self.config['obv_period'])

        # Calculate volume trend
        volume_ma = ta.SMA(volume, timeperiod=20)
        volume_trend = volume / np.maximum(volume_ma, 0.001) - 1

        # Calculate volume-price relationship
        price_change = np.diff(close, n=5, prepend=close[:5])

        for i in range(len(close)):
            if i < 20:  # Need enough data
                continue

            # Volume score based on price-volume relationship
            if price_change[i] > 0 and volume_trend[i] > 0:
                volume_score[i] = volume_trend[i] * 5  # Bullish with increasing volume
            elif price_change[i] < 0 and volume_trend[i] > 0:
                volume_score[i] = -volume_trend[i] * 5  # Bearish with increasing volume
            elif price_change[i] > 0 and volume_trend[i] < 0:
                volume_score[i] = volume_trend[i] * 2  # Bullish with decreasing volume
            elif price_change[i] < 0 and volume_trend[i] < 0:
                volume_score[i] = -volume_trend[i] * 2  # Bearish with decreasing volume

        return volume_score

    def _calculate_swing_analysis(self, high: np.ndarray, low: np.ndarray, close: np.ndarray) -> np.ndarray:
        """Calculate swing point analysis"""
        swing_score = np.zeros(len(close))
        strength = self.config['swing_strength']

        # Simple swing point detection
        for i in range(strength, len(high) - strength):
            # Check for swing high
            is_swing_high = all(high[i] > high[i-j] for j in range(1, strength+1)) and \
                           all(high[i] > high[i+j] for j in range(1, strength+1))

            # Check for swing low
            is_swing_low = all(low[i] < low[i-j] for j in range(1, strength+1)) and \
                          all(low[i] < low[i+j] for j in range(1, strength+1))

            if is_swing_high:
                swing_score[i:i+10] = 2.0  # Bullish swing high
            elif is_swing_low:
                swing_score[i:i+10] = -2.0  # Bearish swing low

        return swing_score

    def _calculate_sentiment_analysis(self, open_prices: np.ndarray, high: np.ndarray,
                                    low: np.ndarray, close: np.ndarray, volume: np.ndarray) -> np.ndarray:
        """Calculate crypto market sentiment analysis"""
        sentiment_score = np.zeros(len(close))

        for i in range(20, len(close)):  # Need enough data
            # Volume-based sentiment
            volume_ratio = volume[i] / np.mean(volume[max(0, i-20):i])
            volume_sentiment = 2.0 if volume_ratio > 2.0 else \
                              1.0 if volume_ratio > 1.5 else \
                              -1.0 if volume_ratio < 0.5 else 0.0

            # Price action sentiment (candle analysis)
            body_size = abs(close[i] - open_prices[i]) / max(ta.ATR(high[:i+1], low[:i+1], close[:i+1], timeperiod=14)[-1], 0.001)
            wick_ratio = abs(high[i] - low[i] - abs(close[i] - open_prices[i])) / max(abs(high[i] - low[i]), 0.001)

            candle_sentiment = 2.0 if close[i] > open_prices[i] and body_size > 0.8 and wick_ratio < 0.3 else \
                              1.0 if close[i] > open_prices[i] and body_size > 0.5 else \
                              -2.0 if close[i] < open_prices[i] and body_size > 0.8 and wick_ratio < 0.3 else \
                              -1.0 if close[i] < open_prices[i] and body_size > 0.5 else 0.0

            # Momentum sentiment
            rsi_change = ta.RSI(close[:i+1], timeperiod=14)[-1] - ta.RSI(close[:i-2], timeperiod=14)[-1]
            momentum_sentiment = 2.0 if rsi_change > 15 else \
                               1.0 if rsi_change > 8 else \
                               -2.0 if rsi_change < -15 else \
                               -1.0 if rsi_change < -8 else 0.0

            # Combine sentiments
            raw_sentiment = (volume_sentiment * 0.3 + candle_sentiment * 0.4 + momentum_sentiment * 0.3)
            sentiment_score[i] = raw_sentiment

        # Smooth sentiment
        sentiment_smoothed = ta.EMA(sentiment_score, timeperiod=self.config['sentiment_lookback'])
        return np.nan_to_num(sentiment_smoothed, nan=0.0)

    def _calculate_trend_scores(self, macd_histogram: np.ndarray, rsi: np.ndarray,
                               di_plus: np.ndarray, di_minus: np.ndarray, adx: np.ndarray,
                               volume_score: np.ndarray, swing_score: np.ndarray,
                               sentiment_score: np.ndarray, close: np.ndarray) -> np.ndarray:
        """Calculate final trend scores"""
        trend_scores = np.full(len(close), 50.0)  # Start with neutral

        # Calculate ATR for normalization
        atr = ta.ATR(np.roll(close, 1), np.roll(close, 1), close, timeperiod=14)
        atr = np.nan_to_num(atr, nan=1.0)

        for i in range(50, len(close)):  # Need enough data for calculations
            # MACD score
            macd_score = 0.0
            if self.config['use_macd'] and atr[i] > 0:
                macd_score = (macd_histogram[i] / atr[i]) * self.config['macd_weight']

            # RSI score
            rsi_score = 0.0
            if self.config['use_rsi']:
                rsi_score = ((rsi[i] - 50) / 50) * self.config['rsi_weight']

            # DMI score
            dmi_score = 0.0
            if self.config['use_dmi']:
                adx_strength = 1.0 if adx[i] > self.config['adx_threshold'] else 0.5
                dmi_score = ((di_plus[i] - di_minus[i]) / 100) * self.config['dmi_weight'] * adx_strength

            # Base trend score
            base_score = macd_score + rsi_score + dmi_score

            # Apply volume impact
            if self.config['use_volume_analysis']:
                base_score = base_score * (1 - self.config['volume_impact']) + \
                           volume_score[i] * self.config['volume_impact']

            # Apply swing impact
            if self.config['use_swing_points']:
                base_score += swing_score[i] * self.config['swing_impact']

            # Apply sentiment impact
            if self.config['use_sentiment_analysis']:
                base_score = base_score * (1 - self.config['sentiment_weight']) + \
                           sentiment_score[i] * self.config['sentiment_weight'] * 2.5

            # Normalize to 0-100 range
            normalized_score = max(0, min(100, 50 + base_score * 10))
            trend_scores[i] = normalized_score

        return trend_scores

    def get_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Get trading signals from the indicator"""
        result = self.calculate(data)

        signals = pd.DataFrame(index=data.index)
        signals['trend_score'] = result['trend_score']
        signals['signal'] = 'HOLD'

        # Generate signals based on trend score
        signals.loc[result['trend_score'] > 70, 'signal'] = 'BUY'
        signals.loc[result['trend_score'] < 30, 'signal'] = 'SELL'
        signals.loc[result['trend_score'] > 80, 'signal'] = 'STRONG_BUY'
        signals.loc[result['trend_score'] < 20, 'signal'] = 'STRONG_SELL'

        # Add signal strength
        signals['signal_strength'] = np.abs(result['trend_score'] - 50) / 50

        # Add trend direction
        signals['trend_direction'] = np.where(result['trend_score'] > 50, 'BULLISH', 'BEARISH')

        return signals

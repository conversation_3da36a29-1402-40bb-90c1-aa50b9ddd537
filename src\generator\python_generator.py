"""
Python Code Generator

Converts Pine Script AST to Python code using templates and function mappings.
"""

from typing import List, Dict, Any, Optional
from ..parser.ast_nodes import PineScriptAST, InputParameter, Variable, FunctionDefinition
from .function_mapper import PineFunctionMapper
from .template_engine import TemplateEngine


class PythonCodeGenerator:
    """Generates Python code from Pine Script AST"""
    
    def __init__(self):
        self.function_mapper = PineFunctionMapper()
        self.template_engine = TemplateEngine()
        self.generated_code = ""
        self.required_imports = set()
        self.custom_functions = []
    
    def generate(self, ast: PineScriptAST) -> str:
        """Generate complete Python code from AST"""
        self.required_imports.clear()
        self.custom_functions.clear()
        
        # Generate class components
        class_name = self.template_engine.generate_class_name(ast.indicator.title)
        imports = self._generate_imports(ast)
        class_header = self._generate_class_header(ast, class_name)
        init_method = self._generate_init_method(ast)
        calculate_method = self._generate_calculate_method(ast)
        custom_functions = self._generate_custom_functions()
        
        # Combine all parts
        self.generated_code = f"{imports}\n\n{class_header}{init_method}{calculate_method}{custom_functions}"
        
        return self.generated_code
    
    def _generate_imports(self, ast: PineScriptAST) -> str:
        """Generate import statements"""
        base_imports = [
            "import pandas as pd",
            "import numpy as np",
            "import talib as ta",
            "from typing import Dict, List, Any, Optional, Tuple"
        ]
        
        # Add additional imports based on functions used
        additional_imports = []
        
        # Check if matplotlib is needed for plotting
        if ast.plots:
            additional_imports.append("import matplotlib.pyplot as plt")
        
        # Check if pandas_ta is needed
        # This would be determined by analyzing the functions used
        additional_imports.append("import pandas_ta")
        
        all_imports = base_imports + additional_imports
        return '\n'.join(all_imports)
    
    def _generate_class_header(self, ast: PineScriptAST, class_name: str) -> str:
        """Generate class header with docstring"""
        template = self.template_engine.templates['class_header']
        
        parameters_doc = self.template_engine.generate_parameter_docs(ast.inputs)
        init_params = self.template_engine.generate_init_params(ast.inputs)
        init_body = self.template_engine.generate_parameter_assignments(ast.inputs)
        
        return template.format(
            title=ast.indicator.title,
            version=ast.version.value,
            class_name=class_name,
            parameters_doc=parameters_doc,
            init_params=init_params,
            init_body=init_body,
            additional_imports=""
        )
    
    def _generate_init_method(self, ast: PineScriptAST) -> str:
        """Generate __init__ method"""
        # This is handled in the class header template
        return ""
    
    def _generate_calculate_method(self, ast: PineScriptAST) -> str:
        """Generate main calculate method"""
        template = self.template_engine.templates['calculate_method']
        
        # Generate result initializations
        result_initializations = self._generate_result_initializations(ast)
        
        # Generate main calculation body
        calculation_body = self._generate_calculation_body(ast)
        
        # Generate result assignments
        result_assignments = self._generate_result_assignments(ast)
        
        return template.format(
            result_initializations=result_initializations,
            calculation_body=calculation_body,
            result_assignments=result_assignments
        )
    
    def _generate_result_initializations(self, ast: PineScriptAST) -> str:
        """Generate result array initializations"""
        initializations = []
        
        # Initialize arrays for each plot
        for plot in ast.plots:
            initializations.append(f"        {plot.series}_values = np.full(self._data_length, np.nan)")
        
        # Initialize arrays for common indicators
        common_indicators = [
            "trend_score", "rsi_values", "macd_line", "macd_signal", 
            "macd_histogram", "adx_values", "di_plus", "di_minus"
        ]
        
        for indicator in common_indicators:
            initializations.append(f"        {indicator} = np.full(self._data_length, np.nan)")
        
        return '\n'.join(initializations) if initializations else "        pass"
    
    def _generate_calculation_body(self, ast: PineScriptAST) -> str:
        """Generate main calculation logic"""
        body_lines = []
        
        # Add basic OHLCV access
        body_lines.extend([
            "            # Current bar data",
            "            current_open = open_prices[i]",
            "            current_high = high_prices[i]", 
            "            current_low = low_prices[i]",
            "            current_close = close_prices[i]",
            "            current_volume = volume[i]",
            "",
            "            # Skip calculation for early bars if needed",
            "            if i < 20:  # Minimum bars for calculations",
            "                continue",
            ""
        ])
        
        # Add indicator calculations based on the Pine Script
        body_lines.extend(self._generate_indicator_calculations())
        
        # Add trend score calculation
        body_lines.extend([
            "",
            "            # Calculate final trend score",
            "            trend_score[i] = self._calculate_trend_score(i, close_prices, high_prices, low_prices, volume)"
        ])
        
        return '\n'.join(body_lines)
    
    def _generate_indicator_calculations(self) -> List[str]:
        """Generate specific indicator calculations"""
        calculations = []
        
        # MACD calculation
        calculations.extend([
            "            # MACD calculation",
            "            if i >= self.macd_slow_length:",
            "                macd_data = ta.MACD(close_prices[:i+1], ",
            "                                   fastperiod=self.macd_fast_length,",
            "                                   slowperiod=self.macd_slow_length,", 
            "                                   signalperiod=self.macd_signal_length)",
            "                macd_line[i] = macd_data[0][-1]",
            "                macd_signal[i] = macd_data[1][-1]",
            "                macd_histogram[i] = macd_data[2][-1]",
            ""
        ])
        
        # RSI calculation
        calculations.extend([
            "            # RSI calculation", 
            "            if i >= self.rsi_length:",
            "                rsi_data = ta.RSI(close_prices[:i+1], timeperiod=self.rsi_length)",
            "                rsi_values[i] = rsi_data[-1]",
            ""
        ])
        
        # DMI calculation
        calculations.extend([
            "            # DMI calculation",
            "            if i >= self.dmi_length:",
            "                di_plus_data, di_minus_data, adx_data = self._dmi(",
            "                    high_prices[:i+1], low_prices[:i+1], close_prices[:i+1],",
            "                    self.dmi_length, self.adx_threshold)",
            "                di_plus[i] = di_plus_data[-1]",
            "                di_minus[i] = di_minus_data[-1]", 
            "                adx_values[i] = adx_data[-1]",
            ""
        ])
        
        return calculations
    
    def _generate_result_assignments(self, ast: PineScriptAST) -> str:
        """Generate result DataFrame assignments"""
        assignments = []
        
        # Add common result columns
        common_results = [
            ("trend_score", "trend_score"),
            ("rsi", "rsi_values"),
            ("macd_line", "macd_line"),
            ("macd_signal", "macd_signal"),
            ("macd_histogram", "macd_histogram"),
            ("adx", "adx_values"),
            ("di_plus", "di_plus"),
            ("di_minus", "di_minus")
        ]
        
        for col_name, var_name in common_results:
            assignments.append(f"        result_df['{col_name}'] = {var_name}")
        
        # Add plot-specific columns
        for plot in ast.plots:
            assignments.append(f"        result_df['{plot.series}'] = {plot.series}_values")
        
        return '\n'.join(assignments) if assignments else "        pass"
    
    def _generate_custom_functions(self) -> str:
        """Generate custom function implementations"""
        template = self.template_engine.templates['custom_functions']
        
        # Add trend score calculation method
        additional_methods = '''
    def _calculate_trend_score(self, index: int, close: np.ndarray, 
                              high: np.ndarray, low: np.ndarray, volume: np.ndarray) -> float:
        """Calculate the main trend score"""
        if index < 50:  # Need enough data
            return 50.0  # Neutral
        
        # Get indicator values at current index
        macd_score = 0.0
        rsi_score = 0.0
        dmi_score = 0.0
        
        # MACD contribution
        if index >= self.macd_slow_length:
            macd_data = ta.MACD(close[:index+1], 
                               fastperiod=self.macd_fast_length,
                               slowperiod=self.macd_slow_length,
                               signalperiod=self.macd_signal_length)
            if len(macd_data[2]) > 0 and not np.isnan(macd_data[2][-1]):
                atr_val = ta.ATR(high[:index+1], low[:index+1], close[:index+1], timeperiod=14)
                if len(atr_val) > 0 and atr_val[-1] > 0:
                    macd_score = macd_data[2][-1] / atr_val[-1] * self.macd_weight
        
        # RSI contribution  
        if index >= self.rsi_length:
            rsi_data = ta.RSI(close[:index+1], timeperiod=self.rsi_length)
            if len(rsi_data) > 0 and not np.isnan(rsi_data[-1]):
                rsi_score = (rsi_data[-1] - 50) / 50 * self.rsi_weight
        
        # DMI contribution
        if index >= self.dmi_length:
            di_plus_data, di_minus_data, adx_data = self._dmi(
                high[:index+1], low[:index+1], close[:index+1],
                self.dmi_length, self.adx_threshold)
            if (len(di_plus_data) > 0 and len(di_minus_data) > 0 and 
                not np.isnan(di_plus_data[-1]) and not np.isnan(di_minus_data[-1])):
                dmi_score = (di_plus_data[-1] - di_minus_data[-1]) / 100 * self.dmi_weight
        
        # Combine scores and normalize to 0-100 range
        combined_score = macd_score + rsi_score + dmi_score
        normalized_score = max(0, min(100, 50 + combined_score * 10))
        
        return normalized_score
'''
        
        return template + additional_methods
    
    def save_to_file(self, filename: str) -> None:
        """Save generated code to file"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(self.generated_code)
    
    def get_function_usage_stats(self, ast: PineScriptAST) -> Dict[str, int]:
        """Get statistics on Pine Script function usage"""
        # This would analyze the AST to count function usage
        # Useful for optimization and debugging
        return {}
